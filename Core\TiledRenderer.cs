using SkiaSharp;
using System;
using System.Collections.Generic;
using System.Collections.Concurrent;
using System.Linq;
using System.Threading.Tasks;
using McLaser.EditViewerSk.Entitys;
using McLaser.EditViewerSk.Spatial;
using McLaser; // 添加Vector2支持

namespace McLaser.EditViewerSk.Core
{
    /// <summary>
    /// 专业级瓦片渲染器：支持多级细节、几何实例化和异步渲染
    /// </summary>
    public class TiledRenderer : IRenderer
    {
        private const int TileSize = 512; // 瓦片大小（像素）
        private const int MaxZoomLevels = 10; // 最大缩放级别
        
        private readonly SKRect _worldBounds;
        private readonly Dictionary<EntityBase, GeometryInfo> _geometryCache = new Dictionary<EntityBase, GeometryInfo>();
        private readonly ConcurrentDictionary<TileKey, TileCache> _tileCache = new ConcurrentDictionary<TileKey, TileCache>();
        private readonly Quadtree _quadtree;
        private readonly object _cacheLock = new object();
        
        // 几何实例化：相同类型的几何体重用
        private readonly Dictionary<GeometryType, GeometryTemplate> _geometryTemplates = new Dictionary<GeometryType, GeometryTemplate>();
        
        // 渲染状态
        private float _lastScale = 1.0f;
        private SKRect _lastViewport = SKRect.Empty;
        private readonly HashSet<EntityBase> _selectedEntities = new HashSet<EntityBase>();

        public TiledRenderer(SKRect worldBounds)
        {
            _worldBounds = worldBounds;
            _quadtree = new Quadtree(0, worldBounds);
            InitializeGeometryTemplates();
        }

        // 几何信息结构
        private struct GeometryInfo
        {
            public GeometryType Type;
            public SKMatrix Transform;
            public SKColor Color;
            public float Scale;
            public bool IsSelected;
        }

        // 瓦片键
        private struct TileKey : IEquatable<TileKey>
        {
            public int X, Y, Level;
            
            public TileKey(int x, int y, int level)
            {
                X = x; Y = y; Level = level;
            }
            
            public bool Equals(TileKey other) => X == other.X && Y == other.Y && Level == other.Level;
            
            public override int GetHashCode() 
            {
                // 手动计算哈希值，兼容.NET Framework 4.7.2
                unchecked
                {
                    int hash = 17;
                    hash = hash * 23 + X;
                    hash = hash * 23 + Y;
                    hash = hash * 23 + Level;
                    return hash;
                }
            }
        }

        // 瓦片缓存
        private class TileCache
        {
            public SKImage CachedImage;
            public long LastAccessTime;
            public bool IsDirty = true;
            public List<EntityBase> ContainedEntities = new List<EntityBase>();
        }

        // 几何类型
        private enum GeometryType
        {
            Circle, Line, Arc, Rectangle, Point
        }

        // 几何模板（用于实例化）
        private class GeometryTemplate
        {
            public SKPath BasePath;
            public SKRect Bounds;
            public GeometryType Type;
        }

        private void InitializeGeometryTemplates()
        {
            // 创建标准几何模板
            _geometryTemplates[GeometryType.Circle] = new GeometryTemplate
            {
                BasePath = CreateUnitCirclePath(),
                Bounds = new SKRect(-1, -1, 1, 1),
                Type = GeometryType.Circle
            };
            
            _geometryTemplates[GeometryType.Point] = new GeometryTemplate
            {
                BasePath = CreatePointPath(),
                Bounds = new SKRect(-2, -2, 2, 2),
                Type = GeometryType.Point
            };
        }

        private SKPath CreateUnitCirclePath()
        {
            var path = new SKPath();
            path.AddCircle(0, 0, 1);
            return path;
        }

        private SKPath CreatePointPath()
        {
            var path = new SKPath();
            path.AddRect(new SKRect(-1, -1, 1, 1));
            return path;
        }

        public void AddEntity(EntityBase entity)
        {
            if (entity == null) return;

            lock (_cacheLock)
            {
                var geometryInfo = CreateGeometryInfo(entity);
                _geometryCache[entity] = geometryInfo;
                _quadtree.Insert(entity);
                
                // 标记相关瓦片为脏
                InvalidateTilesForEntity(entity);
            }
        }

        public void AddEntitiesBatch(IEnumerable<EntityBase> entities)
        {
            var entityList = entities.Where(e => e != null).ToList();
            if (entityList.Count == 0) return;

            lock (_cacheLock)
            {
                foreach (var entity in entityList)
                {
                    var geometryInfo = CreateGeometryInfo(entity);
                    _geometryCache[entity] = geometryInfo;
                    _quadtree.Insert(entity);
                }
                
                // 批量清理瓦片缓存
                ClearTileCache();
            }
        }

        private GeometryInfo CreateGeometryInfo(EntityBase entity)
        {
            var geometryInfo = new GeometryInfo
            {
                Color = entity.IsSelected ? SKColors.Red : SKColors.Black,
                IsSelected = entity.IsSelected
            };

            switch (entity)
            {
                case EntityCircle circle:
                    geometryInfo.Type = GeometryType.Circle;
                    geometryInfo.Transform = SKMatrix.CreateScaleTranslation(
                        (float)circle.Radius, (float)circle.Radius,
                        (float)circle.Center.X, (float)circle.Center.Y);
                    geometryInfo.Scale = (float)circle.Radius;
                    break;
                    
                case EntityPoint point:
                    geometryInfo.Type = GeometryType.Point;
                    geometryInfo.Transform = SKMatrix.CreateTranslation(
                        (float)point.EndPoint.X, (float)point.EndPoint.Y);
                    geometryInfo.Scale = 1.0f;
                    break;
                    
                default:
                    // 其他类型回退到传统渲染
                    geometryInfo.Type = GeometryType.Line;
                    geometryInfo.Transform = SKMatrix.Identity;
                    geometryInfo.Scale = 1.0f;
                    break;
            }

            return geometryInfo;
        }

        public void Render(SKCanvas canvas, SKRect visibleWorldRect)
        {
            var scale = CalculateScale(canvas, visibleWorldRect);
            var zoomLevel = CalculateZoomLevel(scale);
            
            // 始终使用最高质量的实例化渲染
                RenderInstanced(canvas, visibleWorldRect, scale);
            
            // 渲染选中实体（始终使用高质量渲染）
            RenderSelectedEntities(canvas, visibleWorldRect, scale);
        }

        private void RenderInstanced(SKCanvas canvas, SKRect visibleWorldRect, float scale)
        {
            var entities = _quadtree.Query(visibleWorldRect).Cast<EntityBase>()
                .Where(e => e.IsRenderable && e.IsVisible && !e.IsSelected)
                .ToList();
            
            // 按几何类型分组
            var entitiesByType = entities.GroupBy(e => GetEntityGeometryType(e));
            
            foreach (var group in entitiesByType)
            {
                var geometryType = group.Key;
                if (!_geometryTemplates.TryGetValue(geometryType, out var template))
                    continue;
                
                using var paint = CreatePaintForScale(scale, false);
                
                // 批量渲染相同类型的几何体
                foreach (var entity in group)
                {
                    if (_geometryCache.TryGetValue(entity, out var geometryInfo))
                    {
                        canvas.Save();
                        canvas.SetMatrix(in geometryInfo.Transform);
                        paint.Color = geometryInfo.Color;
                        canvas.DrawPath(template.BasePath, paint);
                        canvas.Restore();
                    }
                }
            }
        }

        private void RenderSelectedEntities(SKCanvas canvas, SKRect visibleWorldRect, float scale)
        {
            using var paint = CreatePaintForScale(scale, true);
            
            foreach (var entity in _selectedEntities)
            {
                if (!entity.IsRenderable || !entity.IsVisible) continue;
                if (!visibleWorldRect.IntersectsWith(entity.BoundsSK)) continue;
                
                if (_geometryCache.TryGetValue(entity, out var geometryInfo))
                {
                    var geometryType = geometryInfo.Type;
                    if (_geometryTemplates.TryGetValue(geometryType, out var template))
                    {
                        canvas.Save();
                        canvas.SetMatrix(in geometryInfo.Transform);
                        canvas.DrawPath(template.BasePath, paint);
                        canvas.Restore();
                    }
                }
            }
        }

        private GeometryType GetEntityGeometryType(EntityBase entity)
        {
            return entity switch
            {
                EntityCircle => GeometryType.Circle,
                EntityPoint => GeometryType.Point,
                EntityLine => GeometryType.Line,
                EntityArc => GeometryType.Arc,
                EntityRectangle => GeometryType.Rectangle,
                _ => GeometryType.Line
            };
        }

        private SKPaint CreatePaintForScale(float scale, bool isSelected)
        {
            var strokeWidth = Math.Max(0.5f, 2.0f / scale);
            return new SKPaint
            {
                Style = SKPaintStyle.Stroke,
                StrokeWidth = strokeWidth,
                Color = isSelected ? SKColors.Red : SKColors.Black,
                IsAntialias = scale > 0.1f
            };
        }

        // 其他必需的接口方法
        public void RemoveEntity(EntityBase entity)
        {
            lock (_cacheLock)
            {
                _geometryCache.Remove(entity);
                _selectedEntities.Remove(entity);
                InvalidateTilesForEntity(entity);
                // 重建四叉树
                RebuildQuadtree();
            }
        }

        public void UpdateEntity(EntityBase entity)
        {
            if (entity == null) return;
            
            lock (_cacheLock)
            {
                var geometryInfo = CreateGeometryInfo(entity);
                _geometryCache[entity] = geometryInfo;
                
                if (entity.IsSelected)
                    _selectedEntities.Add(entity);
                else
                    _selectedEntities.Remove(entity);
                    
                InvalidateTilesForEntity(entity);
            }
        }

        public void Clear()
        {
            lock (_cacheLock)
            {
                _geometryCache.Clear();
                _selectedEntities.Clear();
                ClearTileCache();
                _quadtree.Clear();
            }
        }

        // 辅助方法
        private float CalculateScale(SKCanvas canvas, SKRect visibleWorldRect)
        {
            if (visibleWorldRect.IsEmpty) return 1.0f;
            
            var canvasSize = canvas.DeviceClipBounds;
            var scaleX = canvasSize.Width / visibleWorldRect.Width;
            var scaleY = canvasSize.Height / visibleWorldRect.Height;
            
            return Math.Min(scaleX, scaleY);
        }

        private int CalculateZoomLevel(float scale)
        {
            return Math.Max(0, Math.Min(MaxZoomLevels - 1, (int)Math.Log(scale + 1)));
        }

        private List<TileKey> GetVisibleTiles(SKRect visibleRect, int zoomLevel)
        {
            var tiles = new List<TileKey>();
            var tileWorldSize = TileSize * Math.Pow(2, MaxZoomLevels - zoomLevel - 1);
            
            var startX = (int)(visibleRect.Left / tileWorldSize);
            var endX = (int)(visibleRect.Right / tileWorldSize) + 1;
            var startY = (int)(visibleRect.Top / tileWorldSize);
            var endY = (int)(visibleRect.Bottom / tileWorldSize) + 1;
            
            for (int x = startX; x <= endX; x++)
            {
                for (int y = startY; y <= endY; y++)
                {
                    tiles.Add(new TileKey(x, y, zoomLevel));
                }
            }
            
            return tiles;
        }

        private void RenderTiles(SKCanvas canvas, List<TileKey> tiles, float scale)
        {
            // 瓦片渲染实现
            foreach (var tileKey in tiles)
            {
                if (_tileCache.TryGetValue(tileKey, out var tile) && tile.CachedImage != null)
                {
                    var tileWorldSize = TileSize * Math.Pow(2, MaxZoomLevels - tileKey.Level - 1);
                    var rect = new SKRect(
                        (float)(tileKey.X * tileWorldSize),
                        (float)(tileKey.Y * tileWorldSize),
                        (float)((tileKey.X + 1) * tileWorldSize),
                        (float)((tileKey.Y + 1) * tileWorldSize));
                    
                    canvas.DrawImage(tile.CachedImage, rect);
                }
            }
        }

        private void InvalidateTilesForEntity(EntityBase entity)
        {
            // 标记包含此实体的瓦片为脏
            var entityBounds = entity.BoundsSK;
            foreach (var tile in _tileCache.Values)
            {
                if (tile.ContainedEntities.Contains(entity))
                {
                    tile.IsDirty = true;
                }
            }
        }

        private void ClearTileCache()
        {
            foreach (var tile in _tileCache.Values)
            {
                tile.CachedImage?.Dispose();
            }
            _tileCache.Clear();
        }

        private void RebuildQuadtree()
        {
            _quadtree.Clear();
            foreach (var entity in _geometryCache.Keys)
            {
                _quadtree.Insert(entity);
            }
        }

        // 选择功能支持
        public IEnumerable<EntityBase> Query(SKRect queryRect)
        {
            return _quadtree.Query(queryRect).Cast<EntityBase>();
        }

        public void SetSelection(IEnumerable<EntityBase> selectedEntities)
        {
            lock (_cacheLock)
            {
                _selectedEntities.Clear();
                foreach (var entity in selectedEntities)
                {
                    _selectedEntities.Add(entity);
                    if (_geometryCache.ContainsKey(entity))
                    {
                        var info = _geometryCache[entity];
                        info.IsSelected = true;
                        info.Color = SKColors.Red;
                        _geometryCache[entity] = info;
                    }
                }
            }
        }

        public void ClearSelection()
        {
            lock (_cacheLock)
            {
                foreach (var entity in _selectedEntities)
                {
                    if (_geometryCache.ContainsKey(entity))
                    {
                        var info = _geometryCache[entity];
                        info.IsSelected = false;
                        info.Color = SKColors.Black;
                        _geometryCache[entity] = info;
                    }
                }
                _selectedEntities.Clear();
            }
        }

        // 调试方法
        public override string ToString()
        {
            return $"TiledRenderer: {_geometryCache.Count} entities, {_tileCache.Count} tiles, {_selectedEntities.Count} selected";
        }
    }
} 