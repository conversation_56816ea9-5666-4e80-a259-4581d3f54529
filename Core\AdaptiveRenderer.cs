using SkiaSharp;
using System;
using System.Collections.Generic;
using System.Linq;
using McLaser.EditViewerSk.Entitys;
using McLaser.EditViewerSk.Spatial;
using McLaser.EditViewerSk.Base; // 添加Base命名空间引用
using McLaser;
using System.Collections.Concurrent;
using System.Threading.Tasks;

namespace McLaser.EditViewerSk.Core
{
    /// <summary>
    /// 专业级自适应渲染器：基于SkiaSharp Picture + 智能LOD，模仿Rhino渲染品质
    /// 集成GPU加速和路径优化技术，显著提升性能
    /// </summary>
    public class AdaptiveRenderer : I<PERSON><PERSON><PERSON>, IDisposable
    {
        private readonly Quadtree _quadtree;
        private readonly ConcurrentDictionary<EntityBase, GeometryCache> _geometryCache = new ConcurrentDictionary<EntityBase, GeometryCache>();
        private readonly HashSet<EntityBase> _selectedEntities = new HashSet<EntityBase>();
        
        // Picture缓存系统
        private readonly Dictionary<string, SKPicture> _pictureCache = new Dictionary<string, SKPicture>();
        private readonly Dictionary<string, SKPath> _pathTemplateCache = new Dictionary<string, SKPath>();
        private readonly object _cacheLock = new object();
        
        // GPU加速支持
        private readonly GPUContextManager _gpuManager;
        private bool _useGPUAcceleration;
        private bool _gpuInitAttempted = false; // 标记是否已尝试过GPU初始化
        
        // 圆形细分级别 - 更平滑的过渡
        private const int CIRCLE_SEGMENTS_MINIMAL = 6;    // 六边形（而非正方形）
        private const int CIRCLE_SEGMENTS_LOW = 12;       // 12边形
        private const int CIRCLE_SEGMENTS_MEDIUM = 24;    // 24边形
        private const int CIRCLE_SEGMENTS_HIGH = 48;      // 48边形
        private const int CIRCLE_SEGMENTS_ULTRA = 96;     // 96边形
        
        // 性能统计
        public struct RenderStats
        {
            public int TotalEntities;
            public int RenderedEntities;
            public int CacheHits;
            public int CacheMisses;
            public float PixelsPerUnit;
            public int LodLevel;
            public long RenderTimeMs;
            public string LodInfo;
            public int PictureCacheSize;
        }
        
        private RenderStats _lastStats;
        public RenderStats LastRenderStats => _lastStats;

        private struct GeometryCache
        {
            public SKPath CachedPath;
            public float LastScale;
            public int LastLodLevel;
            public bool IsDirty;
            public GeometryType Type;
        }

        private enum GeometryType
        {
            Circle, Line, Point, Arc, Complex
        }

        public AdaptiveRenderer(SKRect worldBounds)
        {
            _quadtree = new Quadtree(0, worldBounds);
            _gpuManager = new GPUContextManager();
            // 延迟GPU初始化到第一次渲染时
            _useGPUAcceleration = false; // 默认为false，在首次渲染时尝试初始化
            
            InitializePathTemplates();
        }

        private void InitializePathTemplates()
        {
            // 预创建不同细节级别的圆形模板 - 更平滑的过渡
            CreateCircleTemplate("circle_6", CIRCLE_SEGMENTS_MINIMAL);
            CreateCircleTemplate("circle_12", CIRCLE_SEGMENTS_LOW);
            CreateCircleTemplate("circle_24", CIRCLE_SEGMENTS_MEDIUM);
            CreateCircleTemplate("circle_48", CIRCLE_SEGMENTS_HIGH);
            CreateCircleTemplate("circle_96", CIRCLE_SEGMENTS_ULTRA);
            
            // 创建点模板 - 更丰富的点样式
            CreatePointTemplate("point_pixel", 0.5f);
            CreatePointTemplate("point_tiny", 1.0f);
            CreatePointTemplate("point_small", 2.0f);
            CreatePointTemplate("point_medium", 3.0f);
            CreatePointTemplate("point_large", 5.0f);
        }

        private void CreateCircleTemplate(string key, int segments)
        {
            var path = new SKPath();
            var angleStep = 2 * Math.PI / segments;
            
            path.MoveTo(1.0f, 0.0f);
            for (int i = 1; i <= segments; i++)
            {
                var angle = i * angleStep;
                path.LineTo((float)Math.Cos(angle), (float)Math.Sin(angle));
            }
            path.Close();
            
            _pathTemplateCache[key] = path;
        }

        private void CreatePointTemplate(string key, float size)
        {
            var path = new SKPath();
            path.AddRect(new SKRect(-size, -size, size * 2, size * 2));
            _pathTemplateCache[key] = path;
        }

        public void AddEntity(EntityBase entity)
        {
            if (entity == null) return;
            
            _quadtree.Insert(entity);
            InvalidatePictureCache();
        }

        public void AddEntitiesBatch(IEnumerable<EntityBase> entities)
        {
            var entityList = entities?.ToList();
            if (entityList == null || entityList.Count == 0) 
            {
                System.Diagnostics.Debug.WriteLine("[AddEntitiesBatch] 没有实体需要添加");
                return;
            }

            System.Diagnostics.Debug.WriteLine($"[AddEntitiesBatch] 开始添加 {entityList.Count} 个实体到四叉树");
            
            int addedCount = 0;
            int skippedCount = 0;
            foreach (var entity in entityList)
            {
                if (entity != null)
                {
                    // 强制重新生成边界，确保边界正确
                    if (entity.IsNeedToRegen)
                    {
                        entity.Regen();
                    }
                    
                    var bounds = entity.BoundsSK;
                    if (!bounds.IsEmpty)
            {
                _quadtree.Insert(entity);
                        addedCount++;
                        
                        // 显示前几个实体的信息
                        if (addedCount <= 5)
                        {
                            System.Diagnostics.Debug.WriteLine($"  添加实体 {addedCount}: {entity.GetType().Name}, Bounds: {bounds}");
                        }
                    }
                    else
                    {
                        skippedCount++;
                        if (skippedCount <= 5)
                        {
                            System.Diagnostics.Debug.WriteLine($"  跳过实体 {skippedCount}: {entity.GetType().Name}, 边界为空, BoundingBox: {entity.BoundingBox}");
                        }
                    }
                }
            }
            
            System.Diagnostics.Debug.WriteLine($"[AddEntitiesBatch] 成功添加 {addedCount} 个实体，跳过 {skippedCount} 个空边界实体");
            
            InvalidatePictureCache();
        }

        public void RemoveEntity(EntityBase entity)
        {
            if (entity == null) return;
            
            // Quadtree没有Remove方法，使用重建策略
            // 获取所有对象，移除目标对象，然后重建
            var allObjects = _quadtree.GetAllObjects();
            var filteredObjects = allObjects.Where(obj => obj != entity).ToList();
            
            _quadtree.Clear();
            foreach (var obj in filteredObjects)
            {
                _quadtree.Insert(obj);
            }
            
            _geometryCache.TryRemove(entity, out _);
            _selectedEntities.Remove(entity);
            InvalidatePictureCache();
        }

        public void UpdateEntity(EntityBase entity)
        {
            if (entity == null) return;
            
            if (_geometryCache.TryGetValue(entity, out var cache))
            {
                cache.IsDirty = true;
                _geometryCache[entity] = cache;
            }
            
            InvalidatePictureCache();
        }

        public void Clear()
        {
            _quadtree.Clear();
            _geometryCache.Clear();
            _selectedEntities.Clear();
            InvalidatePictureCache();
        }

        private void InvalidatePictureCache()
        {
            lock (_cacheLock)
            {
                foreach (var picture in _pictureCache.Values)
                {
                    picture?.Dispose();
                }
                _pictureCache.Clear();
            }
        }

        public void Render(SKCanvas canvas, SKRect visibleWorldRect)
        {
            System.Diagnostics.Debug.WriteLine($"=============== [AdaptiveRenderer.Render] 开始 ===============");
            System.Diagnostics.Debug.WriteLine($"[Render] 参数 - visibleWorldRect: {visibleWorldRect}");
            System.Diagnostics.Debug.WriteLine($"[Render] Canvas状态 - IsClipEmpty: {canvas.IsClipEmpty}, DeviceClipBounds: {canvas.DeviceClipBounds}");
            
            if (_quadtree == null)
            {
                System.Diagnostics.Debug.WriteLine("[Render] 错误：_quadtree为null");
                return;
            }

            // 延迟GPU初始化
            if (!_gpuInitAttempted)
            {
                System.Diagnostics.Debug.WriteLine("[Render] 开始延迟GPU初始化");
                _gpuManager.Initialize();
                _gpuInitAttempted = true;
                System.Diagnostics.Debug.WriteLine($"[Render] GPU初始化完成 - 状态: {_gpuManager.IsInitialized}");
            }

            // 查询可见实体
            System.Diagnostics.Debug.WriteLine("[Render] 开始查询可见实体");
            System.Diagnostics.Debug.WriteLine($"[Render] 查询边界: {visibleWorldRect}");
            
            // 首先检查四叉树状态
            DebugQuadtreeStatus();
            
            var visibleEntities = _quadtree.Query(visibleWorldRect).ToList();
            System.Diagnostics.Debug.WriteLine($"[Render] 查询到 {visibleEntities.Count} 个可见实体");
            
            // 如果查询到0个实体，尝试用更大的边界再次查询
            if (visibleEntities.Count == 0)
            {
                System.Diagnostics.Debug.WriteLine("[Render] 查询到0个实体，尝试大范围查询诊断...");
                
                // 使用整个世界坐标的大边界查询
                var largeBounds = new SKRect(-1000000, -1000000, 2000000, 2000000);
                var allEntitiesLarge = _quadtree.Query(largeBounds).ToList();
                System.Diagnostics.Debug.WriteLine($"[Render] 大范围查询({largeBounds})实体数: {allEntitiesLarge.Count}");
                
                if (allEntitiesLarge.Count > 0)
                {
                    // 显示前几个实体的边界，帮助理解坐标系问题
                    for (int i = 0; i < Math.Min(5, allEntitiesLarge.Count); i++)
                    {
                        var entity = allEntitiesLarge[i] as EntityBase;
                        System.Diagnostics.Debug.WriteLine($"  实体{i+1}: {entity?.GetType().Name}, 边界: {entity?.BoundsSK}");
                    }
                    
                    System.Diagnostics.Debug.WriteLine($"[Render] 坐标系不匹配！查询边界: {visibleWorldRect}, 实体在: 世界坐标系");
                }
            }

            if (visibleEntities.Count == 0)
            {
                System.Diagnostics.Debug.WriteLine("[Render] 没有可见实体，绘制红色测试标记");
                // 绘制测试标记以确认Canvas工作
                using (var testPaint = new SKPaint { Color = SKColors.Red, Style = SKPaintStyle.Fill })
                {
                    var center = new SKPoint(visibleWorldRect.MidX, visibleWorldRect.MidY);
                    var radius = Math.Min(visibleWorldRect.Width, visibleWorldRect.Height) * 0.05f;
                    canvas.DrawCircle(center, radius, testPaint);
                    System.Diagnostics.Debug.WriteLine($"[Render] 绘制测试圆形: 中心({center.X},{center.Y}), 半径{radius}");
                }
                return;
            }

            var stopwatch = System.Diagnostics.Stopwatch.StartNew();

            // 计算当前像素密度
            var pixelsPerUnit = CalculatePixelsPerUnit(canvas, visibleWorldRect);
            var lodLevel = CalculateLodLevel(pixelsPerUnit);

            // 详细调试信息
            System.Diagnostics.Debug.WriteLine($"[AdaptiveRenderer] 总实体数: {visibleEntities.Count}, 可渲染: {visibleEntities.Count}, 视图区域: {visibleWorldRect}");
            
            // 对可见实体进行额外过滤（可渲染性和视锥体裁剪）
            var renderableEntities = visibleEntities
                .Cast<EntityBase>()
                .Where(e => e.IsRenderable && e.IsVisible && IsEntityVisible(e, visibleWorldRect))
                .ToList();
                
            if (renderableEntities.Count == 0)
            {
                System.Diagnostics.Debug.WriteLine("[AdaptiveRenderer] ⚠️ 没有可渲染的实体！检查IsRenderable和IsVisible属性");
                foreach (var entity in visibleEntities.Cast<EntityBase>().Take(5)) // 只显示前5个
                {
                    System.Diagnostics.Debug.WriteLine($"  实体: {entity.GetType().Name}, IsRenderable: {entity.IsRenderable}, IsVisible: {entity.IsVisible}");
                }
                return;
            }
            else if (visibleEntities.Count - renderableEntities.Count > 0)
            {
                System.Diagnostics.Debug.WriteLine($"[AdaptiveRenderer] 视锥体裁剪过滤了 {visibleEntities.Count - renderableEntities.Count} 个实体");
            }

            // 应用实体数限制
            var finalEntities = renderableEntities.Take(Config.MaxRenderEntities).ToList();
            
            // 调试输出
            if (renderableEntities.Count > Config.MaxRenderEntities)
            {
                System.Diagnostics.Debug.WriteLine($"实体数量限制: {renderableEntities.Count} -> {finalEntities.Count} (限制: {Config.MaxRenderEntities})");
            }

            int renderedCount = 0;
            int cacheHits = 0;
            int cacheMisses = 0;
            bool usedGPU = false;

            // 检查GPU可用性
            var isGPUAvailable = _gpuManager.IsInitialized && _gpuManager.IsGPUAvailable;
            System.Diagnostics.Debug.WriteLine($"[Render] GPU可用性: {isGPUAvailable}");

            // 尝试使用GPU加速渲染
            if (isGPUAvailable)
            {
                System.Diagnostics.Debug.WriteLine("[Render] 尝试GPU渲染");
                // 使用设备坐标尺寸而不是世界坐标尺寸
                var deviceBounds = canvas.DeviceClipBounds;
                System.Diagnostics.Debug.WriteLine($"[Render] 使用设备坐标创建GPU表面: {deviceBounds.Width}x{deviceBounds.Height}");
                var gpuSurface = _gpuManager.CreateOrUpdateSurface(
                    deviceBounds.Width, 
                    deviceBounds.Height);
                    
                if (gpuSurface != null)
                {
                    System.Diagnostics.Debug.WriteLine("[Render] GPU表面创建成功，开始GPU渲染");
                    usedGPU = true;
                    RenderWithGPUAcceleration(gpuSurface.Canvas, finalEntities, visibleWorldRect, pixelsPerUnit, lodLevel, out cacheHits, out cacheMisses);
                    _gpuManager.PresentToCanvas(canvas);
                    renderedCount = finalEntities.Count;
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("[Render] GPU表面创建失败，回退到CPU渲染");
                    // 回退到CPU渲染
                    RenderWithCPU(canvas, finalEntities, visibleWorldRect, pixelsPerUnit, lodLevel, out cacheHits, out cacheMisses);
                    renderedCount = finalEntities.Count;
                }
            }
            else
            {
                System.Diagnostics.Debug.WriteLine("[Render] 使用CPU渲染");
                // CPU渲染
                RenderWithCPU(canvas, finalEntities, visibleWorldRect, pixelsPerUnit, lodLevel, out cacheHits, out cacheMisses);
                renderedCount = finalEntities.Count;
            }

            // 渲染选中实体
            RenderSelection(canvas, pixelsPerUnit, lodLevel);

            // 结束GPU帧
            if (isGPUAvailable)
            {
                _gpuManager.EndFrame();
            }

            stopwatch.Stop();

            // 更新统计 - 增加调试信息
            _lastStats = new RenderStats
            {
                TotalEntities = visibleEntities.Count,
                RenderedEntities = renderedCount,
                CacheHits = cacheHits,
                CacheMisses = cacheMisses,
                PixelsPerUnit = pixelsPerUnit,
                LodLevel = lodLevel,
                RenderTimeMs = stopwatch.ElapsedMilliseconds,
                LodInfo = $"{GetLodDescription(lodLevel)} (GPU: {(usedGPU ? "是" : "否")}, 硬件加速: {(_gpuManager.IsHardwareAccelerated ? "是" : "否")})",
                PictureCacheSize = _pictureCache.Count
            };
            
            // 性能警告
            if (stopwatch.ElapsedMilliseconds > 16) // 超过60FPS
            {
                System.Diagnostics.Debug.WriteLine($"渲染性能警告: {stopwatch.ElapsedMilliseconds}ms, 实体: {renderedCount}, GPU: {usedGPU}");
            }
            
            System.Diagnostics.Debug.WriteLine($"=============== [AdaptiveRenderer.Render] 完成 ===============");
            System.Diagnostics.Debug.WriteLine($"[Render] 渲染总结 - 总时间: {stopwatch.ElapsedMilliseconds}ms, 渲染实体: {renderedCount}, GPU: {usedGPU}, 缓存命中: {cacheHits}, 缓存失效: {cacheMisses}");
        }

        private void RenderGeometryMode(SKCanvas canvas, List<EntityBase> entities, SKRect visibleRect, 
            float pixelsPerUnit, int lodLevel, out int cacheHits, out int cacheMisses)
        {
            System.Diagnostics.Debug.WriteLine($"[RenderGeometryMode] 开始渲染，实体数: {entities.Count}");
            System.Diagnostics.Debug.WriteLine($"[RenderGeometryMode] Canvas状态: ClipBounds={canvas.LocalClipBounds}, TotalMatrix={canvas.TotalMatrix}");
            System.Diagnostics.Debug.WriteLine($"[RenderGeometryMode] 可见矩形: {visibleRect}");
            
            cacheHits = 0;
            cacheMisses = 0;
            
            // 按实体类型分组，提高渲染效率
            var entityGroups = entities.GroupBy(e => e.GetType()).ToList();
            
            System.Diagnostics.Debug.WriteLine($"[RenderGeometryMode] 分组数: {entityGroups.Count}");
            
            foreach (var group in entityGroups)
            {
                var entityType = group.Key;
                var groupEntities = group.ToList();
                
                System.Diagnostics.Debug.WriteLine($"[RenderGeometryMode] 处理组: {entityType.Name}, 实体数: {groupEntities.Count}");
                
                // 为每个类型创建专门的缓存键
                var cacheKey = $"geometry_{entityType.Name}_lod{lodLevel}_{visibleRect.GetHashCode()}_{groupEntities.Count}";
                
                SKPicture picture = null;
                lock (_cacheLock)
                {
                    if (_pictureCache.TryGetValue(cacheKey, out picture))
                    {
                        cacheHits++;
                        System.Diagnostics.Debug.WriteLine($"[RenderGeometryMode] 缓存命中: {cacheKey}");
                        canvas.DrawPicture(picture);
                        continue;
                    }
                }
                
                System.Diagnostics.Debug.WriteLine($"[RenderGeometryMode] 缓存未命中，开始创建Picture: {cacheKey}");
                
                // 缓存未命中，创建新的Picture
                using (var recorder = new SKPictureRecorder())
                {
                    var recordingCanvas = recorder.BeginRecording(visibleRect);
                    
                    System.Diagnostics.Debug.WriteLine($"[RenderGeometryMode] 调用RenderEntityGroup，实体数: {groupEntities.Count}");
                    
                    // 批量渲染同类型实体
                    RenderEntityGroup(recordingCanvas, groupEntities, lodLevel, pixelsPerUnit);
                    
                    picture = recorder.EndRecording();
                    cacheMisses++;
                    
                    // 缓存新创建的Picture
                    lock (_cacheLock)
                    {
                        if (_pictureCache.Count > 100) // 限制缓存大小
                        {
                            var oldestKey = _pictureCache.Keys.First();
                            _pictureCache[oldestKey]?.Dispose();
                            _pictureCache.Remove(oldestKey);
                        }
                        _pictureCache[cacheKey] = picture;
                    }
                }
                
                System.Diagnostics.Debug.WriteLine($"[RenderGeometryMode] 绘制Picture到主Canvas");
                canvas.DrawPicture(picture);
            }
            
            System.Diagnostics.Debug.WriteLine($"[RenderGeometryMode] 完成渲染，缓存命中: {cacheHits}, 缓存未命中: {cacheMisses}");
        }

        private void RenderEntityGroup(SKCanvas canvas, List<EntityBase> entities, int lodLevel, float pixelsPerUnit)
        {
            System.Diagnostics.Debug.WriteLine($"[RenderEntityGroup] 开始渲染实体组，数量: {entities.Count}, LOD: {lodLevel}");
            
            // 重置绘制计数器以便调试
            drawCount = 0;
            
            using var paint = CreatePaintForLod(lodLevel);
            
            System.Diagnostics.Debug.WriteLine($"[RenderEntityGroup] 创建Paint完成: 颜色={paint.Color}, 样式={paint.Style}, 线宽={paint.StrokeWidth}");
            
            // 大数据集优化：使用更高效的批量渲染
            if (entities.Count > 100000)
            {
                System.Diagnostics.Debug.WriteLine($"[RenderEntityGroup] 使用大数据集优化渲染");
                // 超过10万实体时，使用高度优化的批量渲染
                RenderLargeDatasetOptimized(canvas, entities, paint);
            }
            else
            {
                System.Diagnostics.Debug.WriteLine($"[RenderEntityGroup] 使用标准渲染模式");
                // 始终使用高质量渲染
                foreach (var entity in entities)
                {
                    DrawEntityDirect(canvas, entity, paint);
                }
            }
            
            System.Diagnostics.Debug.WriteLine($"[RenderEntityGroup] 完成实体组渲染");
        }
        
        private void RenderLargeDatasetOptimized(SKCanvas canvas, List<EntityBase> entities, SKPaint paint)
        {
            System.Diagnostics.Debug.WriteLine($"[RenderLargeDatasetOptimized] 开始优化渲染 {entities.Count} 个实体");
            
            // 按类型分组以优化绘制调用
            var circles = new List<EntityCircle>();
            var lines = new List<EntityLine>();
            var rectangles = new List<EntityRectangle>();
            var others = new List<EntityBase>();
            
            // 快速分类
            foreach (var entity in entities)
            {
                switch (entity)
                {
                    case EntityCircle circle:
                        circles.Add(circle);
                        break;
                    case EntityLine line:
                        lines.Add(line);
                        break;
                    case EntityRectangle rect:
                        rectangles.Add(rect);
                        break;
                    default:
                        others.Add(entity);
                        break;
                }
            }
            
            System.Diagnostics.Debug.WriteLine($"[RenderLargeDatasetOptimized] 分类完成 - 圆形: {circles.Count}, 线段: {lines.Count}, 矩形: {rectangles.Count}, 其他: {others.Count}");
            
            // 批量绘制每种类型
            if (circles.Count > 0)
            {
                System.Diagnostics.Debug.WriteLine($"[RenderLargeDatasetOptimized] 开始绘制 {circles.Count} 个圆形");
                canvas.Save();
                foreach (var circle in circles)
                {
                    canvas.DrawOval(circle.BoundsSK, paint);
                }
                canvas.Restore();
                System.Diagnostics.Debug.WriteLine($"[RenderLargeDatasetOptimized] 完成圆形绘制");
            }
            
            if (lines.Count > 0)
            {
                System.Diagnostics.Debug.WriteLine($"[RenderLargeDatasetOptimized] 开始绘制 {lines.Count} 个线段");
                canvas.Save();
                foreach (var line in lines)
                {
                    var bounds = line.BoundsSK;
                    canvas.DrawLine(bounds.Left, bounds.Top, bounds.Right, bounds.Bottom, paint);
                }
                canvas.Restore();
                System.Diagnostics.Debug.WriteLine($"[RenderLargeDatasetOptimized] 完成线段绘制");
            }
            
            if (rectangles.Count > 0)
            {
                System.Diagnostics.Debug.WriteLine($"[RenderLargeDatasetOptimized] 开始绘制 {rectangles.Count} 个矩形");
                canvas.Save();
                foreach (var rect in rectangles)
                {
                    canvas.DrawRect(rect.BoundsSK, paint);
                }
                canvas.Restore();
                System.Diagnostics.Debug.WriteLine($"[RenderLargeDatasetOptimized] 完成矩形绘制");
            }
            
            // 其他类型
            if (others.Count > 0)
            {
                System.Diagnostics.Debug.WriteLine($"[RenderLargeDatasetOptimized] 开始绘制 {others.Count} 个其他类型实体");
                foreach (var entity in others)
                {
                    DrawEntityDirect(canvas, entity, paint);
                }
                System.Diagnostics.Debug.WriteLine($"[RenderLargeDatasetOptimized] 完成其他类型绘制");
            }
            
            System.Diagnostics.Debug.WriteLine($"[RenderLargeDatasetOptimized] 大数据集优化渲染完成");
        }

        private SKPicture CreateGeometryPicture(List<EntityBase> entities, float pixelsPerUnit, int lodLevel)
        {
            var recorder = new SKPictureRecorder();
            var pictureCanvas = recorder.BeginRecording(SKRect.Create(100000, 100000)); // 大画布
            
            var paint = CreatePaintForLod(lodLevel);
            
            foreach (var entity in entities)
            {
                if (!entity.IsRenderable || !entity.IsVisible) continue;
                
                var path = GetOptimizedPath(entity, pixelsPerUnit, lodLevel);
                if (path != null)
                {
                    pictureCanvas.DrawPath(path, paint);
                }
            }
            
            paint.Dispose();
            var picture = recorder.EndRecording();
            recorder.Dispose();
            
            return picture;
        }

        private SKPath GetOptimizedPath(EntityBase entity, float pixelsPerUnit, int lodLevel)
        {
            // 根据实体类型和LOD级别选择合适的路径
            if (entity is EntityCircle circle)
            {
                var radius = (float)circle.Radius;
                var screenRadius = radius * pixelsPerUnit;
                
                // 根据屏幕大小选择圆形细分级别
                string templateKey;
                if (screenRadius < 2) templateKey = "circle_6";
                else if (screenRadius < 5) templateKey = "circle_12";
                else if (screenRadius < 15) templateKey = "circle_24";
                else if (screenRadius < 50) templateKey = "circle_48";
                else templateKey = "circle_96";
                
                if (_pathTemplateCache.TryGetValue(templateKey, out var template))
                {
                    var path = new SKPath();
                    var matrix = SKMatrix.CreateScaleTranslation(
                        radius, radius,
                        (float)circle.Center.X, (float)circle.Center.Y);
                    template.Transform(in matrix, path);
                    return path;
                }
            }
            else if (entity is EntityPoint point)
            {
                var screenSize = 2.0f * pixelsPerUnit;
                
                string templateKey;
                if (screenSize < 1) templateKey = "point_pixel";
                else if (screenSize < 2) templateKey = "point_tiny";
                else if (screenSize < 4) templateKey = "point_small";
                else if (screenSize < 8) templateKey = "point_medium";
                else templateKey = "point_large";
                
                if (_pathTemplateCache.TryGetValue(templateKey, out var template))
                {
                    var path = new SKPath();
                    var matrix = SKMatrix.CreateTranslation((float)point.EndPoint.X, (float)point.EndPoint.Y);
                    template.Transform(in matrix, path);
                    return path;
                }
            }
            
            // 其他实体类型的处理...
            return null;
        }

        private void RenderSelection(SKCanvas canvas, float pixelsPerUnit, int lodLevel)
        {
            if (_selectedEntities.Count == 0) return;
            
            using var paint = new SKPaint
            {
                Style = SKPaintStyle.Stroke,
                Color = SKColors.Red,
                StrokeWidth = Math.Max(1.0f, 3.0f / pixelsPerUnit),
                IsAntialias = pixelsPerUnit > 1.0f
            };
            
            foreach (var entity in _selectedEntities)
            {
                var path = GetOptimizedPath(entity, pixelsPerUnit, lodLevel);
                if (path != null)
                {
                    canvas.DrawPath(path, paint);
                }
            }
        }
        // 调试信息：记录绘制调用
        private int drawCount = 0;
        private void DrawEntityDirect(SKCanvas canvas, EntityBase entity, SKPaint paint)
        {
            drawCount++;

            // 只对前几个实体输出详细调试信息
            bool isDebugEntity = drawCount <= 5;

            if (isDebugEntity)
            {
                System.Diagnostics.Debug.WriteLine($"[DrawEntityDirect] 绘制第{drawCount}个实体: {entity.GetType().Name}");
                System.Diagnostics.Debug.WriteLine($"  实体边界: {entity.BoundsSK}");
            }
            
            // 直接绘制实体几何形状
            switch (entity)
            {
                case EntityCircle circle:
                    var circleBounds = circle.BoundsSK;
                    if (isDebugEntity)
                    {
                        System.Diagnostics.Debug.WriteLine($"  绘制圆形: {circleBounds}");
                        System.Diagnostics.Debug.WriteLine($"  圆形中心: ({circleBounds.MidX}, {circleBounds.MidY}), 半径: {circleBounds.Width/2}");
                    }
                    canvas.DrawOval(circleBounds, paint);
                    break;

                case EntityLine line:
                    // 使用线段的实际起点和终点，而不是边界框
                    var startX = (float)line.StartPoint.X;
                    var startY = (float)line.StartPoint.Y;
                    var endX = (float)line.EndPoint.X;
                    var endY = (float)line.EndPoint.Y;

                    if (isDebugEntity)
                    {
                        System.Diagnostics.Debug.WriteLine($"  绘制线段: 从({startX},{startY})到({endX},{endY})");
                    }

                    canvas.DrawLine(startX, startY, endX, endY, paint);
                    break;
                    
                case EntityRectangle rect:
                    if (isDebugEntity)
                    {
                        System.Diagnostics.Debug.WriteLine($"  绘制矩形: {rect.BoundsSK}");
                    }
                    canvas.DrawRect(rect.BoundsSK, paint);
                    break;

                case EntityArc arc:
                    if (isDebugEntity)
                    {
                        System.Diagnostics.Debug.WriteLine($"  绘制弧形: {arc.BoundsSK}");
                    }
                    DrawArcDirect(canvas, arc, paint);
                    break;

                case EntityPolyline2D polyline:
                    if (isDebugEntity)
                    {
                        System.Diagnostics.Debug.WriteLine($"  绘制多边形: {polyline.BoundsSK}, 点数: {polyline.Points?.Count ?? 0}");
                    }
                    DrawPolyline2DDirect(canvas, polyline, paint);
                    break;

                default:
                    if (isDebugEntity)
                    {
                        System.Diagnostics.Debug.WriteLine($"[DrawEntityDirect] 未知实体类型: {entity.GetType().Name}, 绘制边界框: {entity.BoundsSK}");
                    }
                    canvas.DrawRect(entity.BoundsSK, paint);
                    break;
            }

            // 定期输出绘制统计
            if (drawCount % 50000 == 0)
            {
                System.Diagnostics.Debug.WriteLine($"[DrawEntityDirect] 已绘制 {drawCount} 个实体");
            }
        }

        private void DrawArcDirect(SKCanvas canvas, EntityArc arc, SKPaint paint)
        {
            var bounds = arc.BoundsSK;
            using var path = new SKPath();
            
            // 简化弧形绘制 - 先绘制完整圆形便于调试
            path.AddOval(bounds);
            canvas.DrawPath(path, paint);
            
            System.Diagnostics.Debug.WriteLine($"[DrawArcDirect] 绘制弧形（临时为圆形）: {bounds}");
        }

        private void DrawPolyline2DDirect(SKCanvas canvas, EntityPolyline2D polyline, SKPaint paint)
        {
            if (polyline.Points != null && polyline.Points.Count > 0)
            {
                var points = polyline.Points.Select(p => new SKPoint((float)p.X, (float)p.Y)).ToArray();
                
                if (points.Length == 1)
                {
                    // 单点绘制为小圆
                    canvas.DrawCircle(points[0], 1, paint);
                }
                else if (points.Length > 1)
                {
                    // 多点绘制为连线
                    using (var path = new SKPath())
                    {
                        path.MoveTo(points[0]);
                        for (int i = 1; i < points.Length; i++)
                        {
                            path.LineTo(points[i]);
                        }
                        
                        if (polyline.IsClosed && points.Length > 2)
                        {
                            path.Close();
                        }
                        
                        canvas.DrawPath(path, paint);
                    }
                }
            }
        }

        private float CalculatePixelsPerUnit(SKCanvas canvas, SKRect worldRect)
        {
            // 获取变换矩阵
            var transform = canvas.TotalMatrix;
            var scaleX = Math.Abs(transform.ScaleX);
            var scaleY = Math.Abs(transform.ScaleY);
            
            // 计算像素密度
            var pixelsPerUnit = Math.Min(scaleX, scaleY);
            
            // 边界检查 - 避免极端值
            const float MIN_PPU = 1e-6f;  // 最小像素密度
            const float MAX_PPU = 1e6f;   // 最大像素密度
            
            if (pixelsPerUnit < MIN_PPU)
            {
                System.Diagnostics.Debug.WriteLine($"像素密度过小: {pixelsPerUnit}, 限制为: {MIN_PPU}");
                pixelsPerUnit = MIN_PPU;
            }
            else if (pixelsPerUnit > MAX_PPU)
            {
                System.Diagnostics.Debug.WriteLine($"像素密度过大: {pixelsPerUnit}, 限制为: {MAX_PPU}");
                pixelsPerUnit = MAX_PPU;
            }
            
            // 检查世界矩形
            if (worldRect.IsEmpty || worldRect.Width <= 0 || worldRect.Height <= 0)
            {
                System.Diagnostics.Debug.WriteLine($"无效的世界矩形: {worldRect}");
                return 1.0f; // 返回默认值
            }
            
            // 调试输出缩放信息
            if (pixelsPerUnit < 0.001f || pixelsPerUnit > 1000f)
            {
                System.Diagnostics.Debug.WriteLine($"极端缩放检测: PPU={pixelsPerUnit:F6}, ScaleX={scaleX:F6}, ScaleY={scaleY:F6}, WorldRect={worldRect}");
            }
            
            return pixelsPerUnit;
        }

        private int CalculateLodLevel(float pixelsPerUnit)
        {
            // 强制使用最高质量渲染 - LOD 5
            return 5; // 始终使用高质量模式
        }

        private SKPaint CreatePaintForLod(int lodLevel)
        {
            return new SKPaint
            {
                Style = SKPaintStyle.Stroke, // 线段和弧形需要使用描边样式
                Color = SKColors.Blue,        // 使用蓝色，更明显
                StrokeWidth = 5.0f,          // 更粗的线宽，确保可见
                IsAntialias = Config.EnableAntiAliasing // 始终启用抗锯齿（如果配置允许）
            };
        }

        private string GetLodDescription(int lodLevel)
        {
            return "高质量渲染"; // 始终使用高质量模式
        }

        public IEnumerable<EntityBase> Query(SKRect queryRect)
        {
            return _quadtree.Query(queryRect).Cast<EntityBase>();
        }

        public void SetSelection(IEnumerable<EntityBase> selectedEntities)
        {
            _selectedEntities.Clear();
            if (selectedEntities != null)
            {
                foreach (var entity in selectedEntities)
                {
                    _selectedEntities.Add(entity);
                }
            }
        }

        public void ClearSelection()
        {
            _selectedEntities.Clear();
        }

        private void RenderWithGPUAcceleration(SKCanvas canvas, List<EntityBase> entities, SKRect visibleRect, 
            float pixelsPerUnit, int lodLevel, out int cacheHits, out int cacheMisses)
        {
            System.Diagnostics.Debug.WriteLine($"[RenderWithGPUAcceleration] 开始GPU渲染，实体数: {entities.Count}");
            System.Diagnostics.Debug.WriteLine($"[RenderWithGPUAcceleration] Canvas信息: ClipBounds={canvas.LocalClipBounds}, TotalMatrix={canvas.TotalMatrix}");
            System.Diagnostics.Debug.WriteLine($"[RenderWithGPUAcceleration] 可见世界矩形: {visibleRect}");
            System.Diagnostics.Debug.WriteLine($"[RenderWithGPUAcceleration] 像素密度: {pixelsPerUnit}");
            
            cacheHits = 0;
            cacheMisses = 0;
            
            // 清空画布
            canvas.Clear(SKColors.White);
            System.Diagnostics.Debug.WriteLine($"[RenderWithGPUAcceleration] 画布已清空为白色");
            
            // 计算世界坐标到屏幕坐标的变换矩阵
            var deviceBounds = canvas.LocalClipBounds;
            var worldToScreenMatrix = CalculateWorldToScreenMatrix(visibleRect, deviceBounds);
            System.Diagnostics.Debug.WriteLine($"[RenderWithGPUAcceleration] 应用世界到屏幕变换矩阵: {worldToScreenMatrix}");
            
            // 测试1：绘制红色矩形（左上角）
            using (var testPaint = new SKPaint { Color = SKColors.Red, Style = SKPaintStyle.Fill })
            {
                var testRect = new SKRect(10, 10, 110, 110);
                canvas.DrawRect(testRect, testPaint);
                System.Diagnostics.Debug.WriteLine($"[RenderWithGPUAcceleration] 绘制红色测试矩形: {testRect}");
            }
            
            // 测试2：绘制绿色圆形（右上角）
            using (var circlePaint = new SKPaint { Color = SKColors.Green, Style = SKPaintStyle.Fill })
            {
                canvas.DrawCircle(900, 100, 50, circlePaint);
                System.Diagnostics.Debug.WriteLine($"[RenderWithGPUAcceleration] 绘制绿色圆形: 中心(900,100), 半径50");
            }
            
            // 测试3：绘制蓝色文字（左下角）
            using (var textPaint = new SKPaint { Color = SKColors.Blue, TextSize = 36, IsAntialias = true })
            {
                canvas.DrawText("GPU渲染测试", 10, 700, textPaint);
                System.Diagnostics.Debug.WriteLine($"[RenderWithGPUAcceleration] 绘制测试文字");
            }
            
            // 测试4：绘制紫色线条（对角线）
            using (var linePaint = new SKPaint { Color = SKColors.Purple, Style = SKPaintStyle.Stroke, StrokeWidth = 5 })
            {
                canvas.DrawLine(0, 0, 200, 200, linePaint);
                System.Diagnostics.Debug.WriteLine($"[RenderWithGPUAcceleration] 绘制紫色对角线");
            }
            
            System.Diagnostics.Debug.WriteLine($"[RenderWithGPUAcceleration] 测试图形绘制完成，开始渲染实际实体");

            // 保存当前Canvas状态，然后应用世界坐标变换
            canvas.Save();
            canvas.SetMatrix(in worldToScreenMatrix);
            System.Diagnostics.Debug.WriteLine($"[RenderWithGPUAcceleration] 已应用世界到屏幕变换矩阵");

            // 现在渲染实际的DXF实体
            System.Diagnostics.Debug.WriteLine($"[RenderWithGPUAcceleration] 开始调用RenderGeometryMode，实体数: {entities.Count}");
            RenderGeometryMode(canvas, entities, visibleRect, pixelsPerUnit, lodLevel, out cacheHits, out cacheMisses);
            System.Diagnostics.Debug.WriteLine($"[RenderWithGPUAcceleration] RenderGeometryMode调用完成");
            
            // 恢复Canvas状态（撤销世界坐标变换）
            canvas.Restore();
            System.Diagnostics.Debug.WriteLine($"[RenderWithGPUAcceleration] 已恢复Canvas状态");
            
            // 最后绘制一个黄色测试图形，确认Canvas状态正常
            using (var finalTestPaint = new SKPaint { Color = SKColors.Yellow, Style = SKPaintStyle.Fill })
            {
                var finalTestRect = new SKRect(600, 50, 700, 150);
                canvas.DrawRect(finalTestRect, finalTestPaint);
                System.Diagnostics.Debug.WriteLine($"[RenderWithGPUAcceleration] 绘制最终黄色测试矩形: {finalTestRect}");
            }
            
            System.Diagnostics.Debug.WriteLine($"[RenderWithGPUAcceleration] GPU渲染完成，准备呈现到主Canvas");
        }

        private void RenderWithCPU(SKCanvas canvas, List<EntityBase> entities, SKRect visibleRect, 
            float pixelsPerUnit, int lodLevel, out int cacheHits, out int cacheMisses)
        {
            // 测试绘制：在Canvas上绘制明显可见的测试图形
            using (var testPaint = new SKPaint { Color = SKColors.Red, Style = SKPaintStyle.Stroke, StrokeWidth = 5 })
            {
                // 在视图中心绘制大红十字
                var centerX = visibleRect.MidX;
                var centerY = visibleRect.MidY;
                var size = Math.Min(visibleRect.Width, visibleRect.Height) * 0.1f;
                
                canvas.DrawLine(centerX - size, centerY, centerX + size, centerY, testPaint);
                canvas.DrawLine(centerX, centerY - size, centerX, centerY + size, testPaint);
                
                System.Diagnostics.Debug.WriteLine($"[RenderWithCPU] 绘制测试十字: 中心({centerX},{centerY}), 大小{size}");
            }
            
            // 使用现有的几何渲染模式，但集成路径优化
            RenderGeometryMode(canvas, entities, visibleRect, pixelsPerUnit, lodLevel, out cacheHits, out cacheMisses);
        }

        private SKColor GetGroupColor(Type entityType)
        {
            return entityType.Name switch
            {
                nameof(EntityCircle) => SKColors.Blue,
                nameof(EntityLine) => SKColors.Green,
                nameof(EntityRectangle) => SKColors.Orange,
                nameof(EntityArc) => SKColors.Purple,
                _ => SKColors.Black
            };
        }
        
        /// <summary>
        /// 计算从世界坐标到屏幕坐标的变换矩阵
        /// </summary>
        private SKMatrix CalculateWorldToScreenMatrix(SKRect worldRect, SKRect screenRect)
        {
            // 计算缩放比例，保持宽高比
            var scaleX = screenRect.Width / worldRect.Width;
            var scaleY = screenRect.Height / worldRect.Height;
            var scale = Math.Min(scaleX, scaleY);
            
            // 计算偏移量，使世界矩形居中显示在屏幕矩形中
            var scaledWorldWidth = worldRect.Width * scale;
            var scaledWorldHeight = worldRect.Height * scale;
            var offsetX = (screenRect.Width - scaledWorldWidth) / 2 - worldRect.Left * scale;
            var offsetY = (screenRect.Height - scaledWorldHeight) / 2 - worldRect.Top * scale;
            
            // 创建变换矩阵：先缩放，再平移
            var matrix = SKMatrix.CreateScale(scale, scale);
            matrix = matrix.PostConcat(SKMatrix.CreateTranslation(offsetX, offsetY));
            
            System.Diagnostics.Debug.WriteLine($"[CalculateWorldToScreenMatrix] 世界矩形: {worldRect}");
            System.Diagnostics.Debug.WriteLine($"[CalculateWorldToScreenMatrix] 屏幕矩形: {screenRect}");
            System.Diagnostics.Debug.WriteLine($"[CalculateWorldToScreenMatrix] 缩放比例: {scale}");
            System.Diagnostics.Debug.WriteLine($"[CalculateWorldToScreenMatrix] 偏移量: ({offsetX}, {offsetY})");
            
            return matrix;
        }

        private void CleanupPictureCache()
        {
            // 清理老旧的缓存项
            var keysToRemove = _pictureCache.Keys.Take(_pictureCache.Count / 3).ToList();
            foreach (var key in keysToRemove)
            {
                _pictureCache[key]?.Dispose();
                _pictureCache.Remove(key);
            }
        }

        private void OnGPUContextChanged(object sender, GPUContextEventArgs e)
        {
            _useGPUAcceleration = e.IsAvailable && e.IsHardwareAccelerated;
            
            if (!_useGPUAcceleration)
            {
                // GPU不可用时清理相关缓存
                InvalidatePictureCache();
            }
        }

        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }
        
        protected virtual void Dispose(bool disposing)
        {
            if (disposing)
            {
                // 清理GPU资源
                _gpuManager?.Dispose();
                
                // 清理Picture缓存
            InvalidatePictureCache();
            
                // 清理路径模板缓存
            foreach (var path in _pathTemplateCache.Values)
            {
                path?.Dispose();
            }
            _pathTemplateCache.Clear();
                
                // 清理路径优化器缓存
                PathOptimizer.ClearCache();
            }
        }

        public void DebugQuadtreeStatus()
        {
            // 使用足够大的查询边界来包含所有可能的实体
            var bounds = new SKRect(-500000, -500000, 500000, 500000);
            var allEntities = _quadtree.Query(bounds).Cast<EntityBase>().ToList();
            
            System.Diagnostics.Debug.WriteLine($"[AdaptiveRenderer] 四叉树调试:");
            System.Diagnostics.Debug.WriteLine($"  查询边界: {bounds}");
            System.Diagnostics.Debug.WriteLine($"  总实体数: {allEntities.Count}");
            
            // 尝试更大的查询范围
            var largeBounds = new SKRect(-1000000, -1000000, 1000000, 1000000);
            var allEntitiesLarge = _quadtree.Query(largeBounds).Cast<EntityBase>().ToList();
            System.Diagnostics.Debug.WriteLine($"  大范围查询({largeBounds})实体数: {allEntitiesLarge.Count}");
            
            if (allEntitiesLarge.Count > 0)
            {
                var entityTypes = allEntitiesLarge.GroupBy(e => e.GetType().Name);
                foreach (var group in entityTypes)
                {
                    System.Diagnostics.Debug.WriteLine($"  {group.Key}: {group.Count()} 个");
                }
                
                var visibleCount = allEntitiesLarge.Count(e => e.IsVisible);
                var renderableCount = allEntitiesLarge.Count(e => e.IsRenderable);
                System.Diagnostics.Debug.WriteLine($"  可见实体: {visibleCount}, 可渲染实体: {renderableCount}");
                
                // 显示前几个实体的边界
                foreach (var entity in allEntitiesLarge.Take(3))
                {
                    System.Diagnostics.Debug.WriteLine($"    实体边界: {entity.BoundsSK}");
                }
            }
        }

        private bool IsEntityVisible(EntityBase entity, SKRect visibleRect)
        {
            // 快速边界检查
            var entityBounds = entity.BoundsSK;
            
            // 如果实体边界为空，跳过
            if (entityBounds.IsEmpty)
                return false;
            
            // 检查实体是否与可见区域相交
            return visibleRect.IntersectsWith(entityBounds);
        }
    }
}