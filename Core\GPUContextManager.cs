using SkiaSharp;
using System;
using McLaser.EditViewerSk.Base;

namespace McLaser.EditViewerSk.Core
{
    /// <summary>
    /// GPU上下文管理器：管理OpenGL集成和硬件加速
    /// 基于研究发现，正确的GPU上下文管理可以提升10倍以上的性能
    /// </summary>
    public class GPUContextManager : IDisposable
    {
        private GRContext _grContext;
        private SKSurface _gpuSurface;
        private SKImageInfo _currentImageInfo;
        private bool _isInitialized;
        private bool _hardwareAcceleration;
        
        // 性能监控
        private long _lastFrameTime;
        private int _frameCount;
        private float _averageFps;
        
        public bool IsGPUAvailable => _grContext != null && !_grContext.IsAbandoned;
        public bool IsHardwareAccelerated => _hardwareAcceleration;
        public bool IsInitialized => _isInitialized;
        public float AverageFPS => _averageFps;
        
        public event EventHandler<GPUContextEventArgs> ContextChanged;
        
        /// <summary>
        /// 初始化GPU上下文
        /// </summary>
        public bool Initialize()
        {
            try
            {
                // 直接尝试创建OpenGL上下文
                _grContext = GRContext.CreateGl();
                
                if (_grContext == null)
                {
                    Console.WriteLine("Failed to create OpenGL context - no active GL context available");
                    return false;
                }
                
                // 检查硬件加速支持
                _hardwareAcceleration = CheckHardwareAcceleration();
                _isInitialized = true;
                
                Console.WriteLine($"GPU初始化成功 - 硬件加速: {_hardwareAcceleration}");
                
                OnContextChanged(new GPUContextEventArgs
                {
                    IsAvailable = true,
                    IsHardwareAccelerated = _hardwareAcceleration,
                    Message = $"GPU context initialized successfully (Hardware: {_hardwareAcceleration})"
                });
                
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"GPU initialization failed: {ex.Message}");
                
                // 清理失败的上下文
                if (_grContext != null)
                {
                    try
                    {
                        _grContext.Dispose();
                    }
                    catch { }
                    _grContext = null;
                }
                
                OnContextChanged(new GPUContextEventArgs
                {
                    IsAvailable = false,
                    IsHardwareAccelerated = false,
                    Message = $"GPU initialization failed: {ex.Message}"
                });
                return false;
            }
        }
        
        /// <summary>
        /// 创建或更新GPU表面
        /// </summary>
        public SKSurface CreateOrUpdateSurface(int width, int height, SKColorType colorType = SKColorType.Rgba8888)
        {
            if (!_isInitialized || _grContext == null)
            {
                return null;
            }
            
            // 验证尺寸
            if (width <= 0 || height <= 0)
            {
                System.Diagnostics.Debug.WriteLine($"无效的表面尺寸: {width}x{height}");
                return null;
            }
            
            // 限制最大尺寸以避免内存问题
            const int MAX_SURFACE_SIZE = 16384; // 增加到16K，对于现代GPU更合理
            if (width > MAX_SURFACE_SIZE || height > MAX_SURFACE_SIZE)
            {
                // 保持宽高比的缩放
                float aspectRatio = (float)width / height;
                if (width > height)
                {
                    width = MAX_SURFACE_SIZE;
                    height = (int)(MAX_SURFACE_SIZE / aspectRatio);
                }
                else
                {
                    height = MAX_SURFACE_SIZE;
                    width = (int)(MAX_SURFACE_SIZE * aspectRatio);
                }
                System.Diagnostics.Debug.WriteLine($"表面尺寸缩放: 保持宽高比缩放到 {width}x{height}");
            }
            
            var newImageInfo = new SKImageInfo(width, height, colorType, SKAlphaType.Premul);
            
            // 检查是否需要重建表面
            if (_gpuSurface == null || _currentImageInfo != newImageInfo)
            {
                _gpuSurface?.Dispose();
                
                try
                {
                    _gpuSurface = SKSurface.Create(_grContext, true, newImageInfo);
                    
                    if (_gpuSurface != null)
                    {
                        _currentImageInfo = newImageInfo;
                        System.Diagnostics.Debug.WriteLine($"GPU表面创建成功: {width}x{height}");
                    }
                    else
                    {
                        System.Diagnostics.Debug.WriteLine($"GPU表面创建失败: {width}x{height}");
                    }
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"GPU表面创建异常: {ex.Message}");
                    _gpuSurface = null;
                }
            }
            
            return _gpuSurface;
        }
        
        /// <summary>
        /// 优化GPU设置以获得最佳性能
        /// </summary>
        public void OptimizeForPerformance()
        {
            if (!IsGPUAvailable)
                return;
                
            try
            {
                // 设置GPU缓存限制
                var cacheLimit = Config.RenderQualityLevel switch
                {
                    Base.RenderQuality.Performance => 32 * 1024 * 1024,  // 32MB
                    Base.RenderQuality.Low => 64 * 1024 * 1024,          // 64MB
                    Base.RenderQuality.Medium => 128 * 1024 * 1024,      // 128MB
                    Base.RenderQuality.High => 256 * 1024 * 1024,        // 256MB
                    Base.RenderQuality.Ultra => 512 * 1024 * 1024,       // 512MB
                    _ => 128 * 1024 * 1024
                };
                
                _grContext.SetResourceCacheLimit(cacheLimit);
                
                // 启用异步GPU操作（如果支持）
                if (_hardwareAcceleration)
                {
                    // 预热GPU管线
                    WarmupGPUPipeline();
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"GPU optimization failed: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 开始新的渲染帧
        /// </summary>
        public void BeginFrame()
        {
            _lastFrameTime = GetHighResolutionTime();
            
            if (IsGPUAvailable)
            {
                _grContext.ResetContext();
            }
        }
        
        /// <summary>
        /// 结束当前渲染帧并刷新GPU命令
        /// </summary>
        public void EndFrame()
        {
            if (IsGPUAvailable)
            {
                _grContext.Flush();
                
                // 更新性能统计
                UpdatePerformanceStats();
            }
        }
        
        /// <summary>
        /// 将GPU表面内容复制到目标画布
        /// </summary>
        public void PresentToCanvas(SKCanvas targetCanvas)
        {
            System.Diagnostics.Debug.WriteLine($"[PresentToCanvas] 开始将GPU表面内容复制到目标Canvas");
            
            if (_gpuSurface != null)
            {
                System.Diagnostics.Debug.WriteLine($"[PresentToCanvas] GPU表面可用，大小: {_currentImageInfo.Width}x{_currentImageInfo.Height}");
                System.Diagnostics.Debug.WriteLine($"[PresentToCanvas] 目标Canvas状态: ClipBounds={targetCanvas.LocalClipBounds}, TotalMatrix={targetCanvas.TotalMatrix}");
                
                using var image = _gpuSurface.Snapshot();
                System.Diagnostics.Debug.WriteLine($"[PresentToCanvas] 创建表面快照成功，图像大小: {image.Width}x{image.Height}");
                
                // 保存当前变换状态
                targetCanvas.Save();
                
                // 重置变换矩阵到屏幕坐标系，因为GPU表面内容是在屏幕坐标系中渲染的
                targetCanvas.ResetMatrix();
                System.Diagnostics.Debug.WriteLine($"[PresentToCanvas] 已重置目标Canvas变换矩阵到屏幕坐标系");
                
                // 测试：绘制一个红色边框来验证位置
                using (var testPaint = new SKPaint { Color = SKColors.Red, Style = SKPaintStyle.Stroke, StrokeWidth = 5 })
                {
                    var testRect = new SKRect(0, 0, image.Width, image.Height);
                    targetCanvas.DrawRect(testRect, testPaint);
                    System.Diagnostics.Debug.WriteLine($"[PresentToCanvas] 绘制红色测试边框: {testRect}");
                }
                
                targetCanvas.DrawImage(image, 0, 0);
                System.Diagnostics.Debug.WriteLine($"[PresentToCanvas] 图像绘制到目标Canvas完成，位置: (0,0)");
                
                // 恢复原始变换状态
                targetCanvas.Restore();
                System.Diagnostics.Debug.WriteLine($"[PresentToCanvas] 已恢复目标Canvas原始变换状态");
            }
            else
            {
                System.Diagnostics.Debug.WriteLine($"[PresentToCanvas] 错误：GPU表面为null");
            }
        }
        
        /// <summary>
        /// 清理GPU资源
        /// </summary>
        public void Cleanup()
        {
            if (IsGPUAvailable)
            {
                _grContext.PurgeResources();
            }
        }
        
        /// <summary>
        /// 检查内存使用情况
        /// </summary>
        public GPUMemoryInfo GetMemoryInfo()
        {
            if (!IsGPUAvailable)
            {
                return new GPUMemoryInfo { IsAvailable = false };
            }
            
            try
            {
                _grContext.GetResourceCacheUsage(out int resourceCount, out long resourceBytes);
                var resourceBytesLimit = (long)_grContext.GetResourceCacheLimit();
                
                return new GPUMemoryInfo
                {
                    IsAvailable = true,
                    ResourceCount = resourceCount,
                    UsedBytes = resourceBytes,
                    LimitBytes = resourceBytesLimit,
                    UsagePercentage = resourceBytesLimit > 0 ? (float)resourceBytes / resourceBytesLimit * 100 : 0
                };
            }
            catch
            {
                return new GPUMemoryInfo { IsAvailable = false };
            }
        }
        
        private bool CheckHardwareAcceleration()
        {
            if (_grContext == null)
                return false;
                
            try
            {
                // 创建一个小的测试表面来验证硬件加速
                var testInfo = new SKImageInfo(64, 64, SKColorType.Rgba8888);
                using var testSurface = SKSurface.Create(_grContext, true, testInfo);
                
                if (testSurface?.Canvas != null)
                {
                    // 执行简单的绘制测试
                    testSurface.Canvas.Clear(SKColors.Red);
                    testSurface.Canvas.DrawCircle(32, 32, 16, new SKPaint { Color = SKColors.Blue });
                    testSurface.Canvas.Flush();
                    
                    return true;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Hardware acceleration test failed: {ex.Message}");
            }
            
            return false;
        }
        
        private void WarmupGPUPipeline()
        {
            try
            {
                // 创建小的预热表面
                var warmupInfo = new SKImageInfo(256, 256, SKColorType.Rgba8888);
                using var warmupSurface = SKSurface.Create(_grContext, true, warmupInfo);
                
                if (warmupSurface?.Canvas != null)
                {
                    var canvas = warmupSurface.Canvas;
                    using var paint = new SKPaint { IsAntialias = true };
                    
                    // 预热各种绘制操作
                    canvas.Clear(SKColors.White);
                    canvas.DrawCircle(64, 64, 32, paint);
                    canvas.DrawRect(128, 128, 64, 64, paint);
                    canvas.DrawLine(0, 0, 256, 256, paint);
                    
                    canvas.Flush();
                    _grContext.Flush();
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"GPU warmup failed: {ex.Message}");
            }
        }
        
        private void UpdatePerformanceStats()
        {
            _frameCount++;
            var currentTime = GetHighResolutionTime();
            var frameTime = currentTime - _lastFrameTime;
            
            if (frameTime > 0)
            {
                var currentFps = 1000.0f / frameTime;
                _averageFps = (_averageFps * (_frameCount - 1) + currentFps) / _frameCount;
                
                // 重置统计以避免溢出
                if (_frameCount > 1000)
                {
                    _frameCount = 0;
                    _averageFps = currentFps;
                }
            }
        }
        
        private void OnContextChanged(GPUContextEventArgs args)
        {
            ContextChanged?.Invoke(this, args);
        }
        
        public void Dispose()
        {
            _gpuSurface?.Dispose();
            _grContext?.Dispose();
            _isInitialized = false;
        }

        private long GetHighResolutionTime()
        {
            // 对于.NET Framework兼容性，使用Environment.TickCount
            // 虽然精度较低，但足够用于性能监控
            return Environment.TickCount;
        }
    }
    
    public class GPUContextEventArgs : EventArgs
    {
        public bool IsAvailable { get; set; }
        public bool IsHardwareAccelerated { get; set; }
        public string Message { get; set; }
    }
    
    public struct GPUMemoryInfo
    {
        public bool IsAvailable;
        public int ResourceCount;
        public long UsedBytes;
        public long LimitBytes;
        public float UsagePercentage;
        
        public string UsedMB => $"{UsedBytes / (1024.0 * 1024.0):F1} MB";
        public string LimitMB => $"{LimitBytes / (1024.0 * 1024.0):F1} MB";
    }
} 