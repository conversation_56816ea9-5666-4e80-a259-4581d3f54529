
using SkiaSharp;
using McLaser.EditViewerSk.Entitys;
using System.Collections.Generic;

namespace McLaser.EditViewerSk.Core
{
    /// <summary>
    /// Defines the contract for a renderer that can draw entities onto a Skia canvas.
    /// </summary>
    public interface IRenderer
    {
        /// <summary>
        /// Adds a single entity to the renderer's cache.
        /// </summary>
        /// <param name="entity">The entity to add.</param>
        void AddEntity(EntityBase entity);

        /// <summary>
        /// Adds multiple entities in batch for better performance.
        /// </summary>
        /// <param name="entities">The entities to add.</param>
        void AddEntitiesBatch(IEnumerable<EntityBase> entities);

        /// <summary>
        /// Removes an entity from the renderer's cache.
        /// </summary>
        /// <param name="entity">The entity to remove.</param>
        void RemoveEntity(EntityBase entity);

        /// <summary>
        /// Updates the cached representation of an entity.
        /// </summary>
        /// <param name="entity">The entity to update.</param>
        void UpdateEntity(EntityBase entity);

        /// <summary>
        /// Clears all cached geometry.
        /// </summary>
        void Clear();

        /// <summary>
        /// Renders all cached entities onto the canvas.
        /// </summary>
        /// <param name="canvas">The Skia canvas to draw on.</param>
        /// <param name="visibleWorldRect">The visible world rectangle.</param>
        void Render(SKCanvas canvas, SKRect visibleWorldRect);

        /// <summary>
        /// Queries entities within a rectangle.
        /// </summary>
        /// <param name="queryRect">The query rectangle.</param>
        /// <returns>Entities within the rectangle.</returns>
        IEnumerable<EntityBase> Query(SKRect queryRect);

        /// <summary>
        /// Sets the selection state for entities.
        /// </summary>
        /// <param name="selectedEntities">The entities to select.</param>
        void SetSelection(IEnumerable<EntityBase> selectedEntities);

        /// <summary>
        /// Clears the current selection.
        /// </summary>
        void ClearSelection();
    }
}
