﻿using SkiaSharp;
using Newtonsoft.Json;
using System.ComponentModel;



namespace McLaser.EditViewerSk.Base
{
    public class Config
    {
        public static double ZoomMax = 1e4;
        public static double ZoomMin = 1e-4;
        public static float ScaleIncrement = 0.1f;

        public static int DocumentGridInterval = 10;

        public static int UndoStackSize = 100;

        public static double AngleFactor = 1;
        public static string DocumentVersion = "V0.1";

        [Description("笔号颜色 \r默认: 10 Colors")]
        [JsonIgnore]
        [Browsable(true)]
        [ReadOnly(true)]
        [Category("Pen")]
        [DisplayName("Colors")]
        public static SKColor[] PenColors { get; } = new SKColor[]
        {
            SKColors.White,
            SKColors.Gray,
            SKColors.Orange,
            SKColors.Yellow,
            SKColors.Blue,
            SKColors.Magenta,
            SKColors.Cyan,
            SKColors.Orchid,
            SKColors.Red,
            SKColors.Green
        };

        [Category("View")]
        [DisplayName("Color (entity)")]
        [Description("Color of Default Entity\rDefault: White (PenColors[0])")]
        [JsonIgnore,Browsable(true), ReadOnly(false)]
        public static SKColor ViewDefaultEntityColor { get; set; } = Config.PenColors[0];

        // 渲染质量设置
        [Category("Render")]
        [DisplayName("渲染质量")]
        [Description("设置渲染质量级别\r可选值: Ultra, High, Medium, Low, Performance")]
        [JsonIgnore, Browsable(true), ReadOnly(false)]
        public static RenderQuality RenderQualityLevel { get; set; } = RenderQuality.High;

        [Category("Render")]
        [DisplayName("抗锯齿")]
        [Description("启用图形抗锯齿以获得更平滑的显示效果")]
        [JsonIgnore, Browsable(true), ReadOnly(false)]
        public static bool EnableAntiAliasing { get; set; } = true;

        [Category("Render")]
        [DisplayName("最大渲染实体数")]
        [Description("单次渲染的最大实体数量限制")]
        [JsonIgnore, Browsable(true), ReadOnly(false)]
        public static int MaxRenderEntities { get; set; } = 500000; // 增加到50万，避免大场景时图形消失
    }

    /// <summary>
    /// 渲染质量枚举
    /// </summary>
    public enum RenderQuality
    {
        /// <summary>性能优先，使用最少渲染细节</summary>
        Performance = 0,
        /// <summary>低质量，基本图形显示</summary>
        Low = 1,
        /// <summary>中等质量，平衡性能和效果</summary>
        Medium = 2,
        /// <summary>高质量，优秀的视觉效果</summary>
        High = 3,
        /// <summary>超高质量，最佳视觉效果但性能较低</summary>
        Ultra = 4
    }
}
