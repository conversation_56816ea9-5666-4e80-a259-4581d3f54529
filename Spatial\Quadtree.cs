
using System.Collections.Generic;
using SkiaSharp;

namespace McLaser.EditViewerSk.Spatial
{
    public class Quadtree
    {
        // 优化：大幅减少每个节点的对象数量，提高分割效率
        private const int MaxObjects = 1000; // 从500000减少到1000
        private const int MaxLevels = 15;     // 从10增加到15，支持更细的分割

        private readonly int _level;
        private readonly SKRect _bounds;
        private readonly List<IQuadtreeObject> _objects;
        private readonly Quadtree[] _nodes;

        // 缓存查询结果以提高性能
        private readonly Dictionary<int, List<IQuadtreeObject>> _queryCache = new Dictionary<int, List<IQuadtreeObject>>();
        private SKRect _lastQueryRect = SKRect.Empty;
        private List<IQuadtreeObject> _lastQueryResult = null;

        public Quadtree(int level, SKRect bounds)
        {
            _level = level;
            _bounds = bounds;
            _objects = new List<IQuadtreeObject>(MaxObjects); // 预分配容量
            _nodes = new Quadtree[4];
        }

        public void Clear()
        {
            _objects.Clear();
            _queryCache.Clear();
            _lastQueryResult = null;
            _lastQueryRect = SKRect.Empty;
            
            for (int i = 0; i < _nodes.Length; i++)
            {
                if (_nodes[i] != null)
                {
                    _nodes[i].Clear();
                    _nodes[i] = null;
                }
            }
        }

        private void Split()
        {
            float subWidth = _bounds.Width / 2;
            float subHeight = _bounds.Height / 2;
            float x = _bounds.Left;
            float y = _bounds.Top;

            _nodes[0] = new Quadtree(_level + 1, new SKRect(x + subWidth, y, x + 2 * subWidth, y + subHeight));             // Top-right
            _nodes[1] = new Quadtree(_level + 1, new SKRect(x, y, x + subWidth, y + subHeight));                         // Top-left
            _nodes[2] = new Quadtree(_level + 1, new SKRect(x, y + subHeight, x + subWidth, y + 2 * subHeight));             // Bottom-left
            _nodes[3] = new Quadtree(_level + 1, new SKRect(x + subWidth, y + subHeight, x + 2 * subWidth, y + 2 * subHeight)); // Bottom-right
        }

        private int GetIndex(IQuadtreeObject obj)
        {
            var objBounds = obj.BoundsSK;
            if (objBounds.IsEmpty) return -1;

            int index = -1;
            float verticalMidpoint = _bounds.Left + (_bounds.Width / 2);
            float horizontalMidpoint = _bounds.Top + (_bounds.Height / 2);

            bool topQuadrant = objBounds.Bottom < horizontalMidpoint && objBounds.Top < horizontalMidpoint;
            bool bottomQuadrant = objBounds.Top > horizontalMidpoint;

            if (objBounds.Right < verticalMidpoint)
            {
                if (topQuadrant) index = 1; // Top-left
                else if (bottomQuadrant) index = 2; // Bottom-left
            }
            else if (objBounds.Left > verticalMidpoint)
            {
                if (topQuadrant) index = 0; // Top-right
                else if (bottomQuadrant) index = 3; // Bottom-right
            }
            return index;
        }

        public void Insert(IQuadtreeObject obj)
        {
            if (_nodes[0] != null)
            {
                int index = GetIndex(obj);
                if (index != -1)
                {
                    _nodes[index].Insert(obj);
                    return;
                }
            }

            _objects.Add(obj);
            
            // 调试信息：显示前几个添加的对象
            if (_objects.Count <= 5)
            {
                System.Diagnostics.Debug.WriteLine($"[Quadtree.Insert] 第{_objects.Count}个对象添加到根节点: {obj.GetType().Name}, 边界: {obj.BoundsSK}");
            }

            if (_objects.Count > MaxObjects && _level < MaxLevels)
            {
                if (_nodes[0] == null) Split();

                // *** OPTIMIZED AND CORRECTED REDISTRIBUTION LOGIC ***
                var objectsToRedistribute = new List<IQuadtreeObject>(_objects);
                _objects.Clear();

                foreach (var item in objectsToRedistribute)
                {
                    int index = GetIndex(item);
                    if (index != -1)
                    {
                        _nodes[index].Insert(item);
                    }
                    else
                    {
                        _objects.Add(item);
                    }
                }
                
                System.Diagnostics.Debug.WriteLine($"[Quadtree.Insert] 达到分割阈值，重新分配 {objectsToRedistribute.Count} 个对象，当前根节点保留 {_objects.Count} 个");
            }
            
            // 定期输出统计信息
            if (_objects.Count % 50000 == 0 && _objects.Count > 0)
            {
                System.Diagnostics.Debug.WriteLine($"[Quadtree.Insert] 根节点当前包含 {_objects.Count} 个对象");
            }
        }

        public List<IQuadtreeObject> Query(SKRect area)
        {
            // 缓存优化：如果查询区域相同，返回缓存结果
            if (_lastQueryResult != null && _lastQueryRect.Equals(area))
            {
                return _lastQueryResult;
            }

            List<IQuadtreeObject> result = new List<IQuadtreeObject>();
            QueryOptimized(area, result);
            
            // 缓存查询结果
            _lastQueryRect = area;
            _lastQueryResult = result;
            
            return result;
        }

        public List<IQuadtreeObject> GetAllObjects()
        {
            List<IQuadtreeObject> result = new List<IQuadtreeObject>();
            GetAllObjects(result);
            return result;
        }

        private void GetAllObjects(List<IQuadtreeObject> result)
        {
            result.AddRange(_objects);
            if (_nodes[0] != null)
            {
                foreach (var node in _nodes)
                {
                    node.GetAllObjects(result);
                }
            }
        }

        // 优化的查询方法：减少重复检查和内存分配
        private void QueryOptimized(SKRect area, List<IQuadtreeObject> list)
        {
            // 预过滤：如果查询区域与节点边界不相交，直接返回
            if (!area.IntersectsWith(_bounds))
                return;

            // 调试信息：记录查询到的对象数量
            int foundInThisNode = 0;

            // 批量检查当前节点的对象
            for (int i = 0; i < _objects.Count; i++)
            {
                var obj = _objects[i];
                if (area.IntersectsWith(obj.BoundsSK))
                {
                    list.Add(obj);
                    foundInThisNode++;
                }
            }
            
            // 调试信息：输出节点查询统计
            if (_level == 0 && (_objects.Count > 0 || foundInThisNode > 0))
            {
                System.Diagnostics.Debug.WriteLine($"[Quadtree.Query] 根节点: 包含{_objects.Count}个对象, 查询到{foundInThisNode}个");
            }

            // 递归查询子节点
            if (_nodes[0] != null)
            {
                if (_level == 0) // 只在根节点输出调试信息
                {
                    System.Diagnostics.Debug.WriteLine($"[Quadtree.Query] 根节点有子节点，查询区域: {area}");
                    for (int i = 0; i < 4; i++)
                    {
                        System.Diagnostics.Debug.WriteLine($"  子节点{i}: 边界={_nodes[i]._bounds}, 对象数={_nodes[i]._objects.Count}, 相交={_nodes[i]._bounds.IntersectsWith(area)}");
                    }
                }
                
                for (int i = 0; i < 4; i++)
                {
                    if (_nodes[i]._bounds.IntersectsWith(area))
                    {
                        int beforeCount = list.Count;
                        _nodes[i].QueryOptimized(area, list);
                        int addedCount = list.Count - beforeCount;
                        
                        if (_level == 0 && addedCount > 0) // 只在根节点输出调试信息
                        {
                            System.Diagnostics.Debug.WriteLine($"  子节点{i}查询到 {addedCount} 个对象");
                        }
                    }
                }
            }
            else if (_level == 0)
            {
                System.Diagnostics.Debug.WriteLine($"[Quadtree.Query] 根节点没有子节点");
            }
        }
    }
}
