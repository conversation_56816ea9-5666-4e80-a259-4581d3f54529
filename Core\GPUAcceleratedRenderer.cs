using SkiaSharp;
using SkiaSharp.Views.Desktop;
using System;
using System.Collections.Generic;
using System.Linq;
using McLaser.EditViewerSk.Entitys;
using McLaser.EditViewerSk.Spatial;
using McLaser.EditViewerSk.Base;
using System.Collections.Concurrent;
using System.Threading.Tasks;

namespace McLaser.EditViewerSk.Core
{
    /// <summary>
    /// GPU加速渲染器：充分利用SkiaSharp的硬件加速功能
    /// 使用SKGLContext、批量渲染、路径合并和实例化技术
    /// </summary>
    public class GPUAcceleratedRenderer : I<PERSON><PERSON>er, IDisposable
    {
        private readonly Quadtree _quadtree;
        private readonly SKRect _worldBounds;
        private readonly HashSet<EntityBase> _selectedEntities = new HashSet<EntityBase>();
        
        // GPU加速相关
        private GRContext _grContext;
        private SKSurface _gpuSurface;
        private SKImageInfo _surfaceInfo;
        
        // 批量渲染缓存
        private readonly Dictionary<Type, List<EntityBase>> _entityBatches = new Dictionary<Type, List<EntityBase>>();
        private readonly Dictionary<string, SKPicture> _batchPictureCache = new Dictionary<string, SKPicture>();
        private readonly object _cacheLock = new object();
        
        // 路径合并和实例化
        private readonly Dictionary<string, SKPath> _pathTemplates = new Dictionary<string, SKPath>();
        private readonly Dictionary<Type, GeometryTemplate> _geometryTemplates = new Dictionary<Type, GeometryTemplate>();
        
        // 对象池 - 减少GC压力
        private readonly ConcurrentBag<SKPaint> _paintPool = new ConcurrentBag<SKPaint>();
        private readonly ConcurrentBag<SKPath> _pathPool = new ConcurrentBag<SKPath>();
        
        // 性能统计
        private RenderStats _lastStats;
        
        public struct RenderStats
        {
            public int TotalEntities;
            public int RenderedEntities;
            public int BatchedDrawCalls;
            public int CacheHits;
            public int CacheMisses;
            public long RenderTimeMs;
            public long GPUTimeMs;
            public bool UsingGPU;
            public string OptimizationInfo;
        }
        
        private struct GeometryTemplate
        {
            public SKPath BasePath;
            public SKMatrix BaseTransform;
            public float BaseSize;
            public GeometryType Type;
        }
        
        private enum GeometryType
        {
            Circle, Rectangle, Line, Arc, Complex
        }
        
        public RenderStats LastRenderStats => _lastStats;
        
        public GPUAcceleratedRenderer(SKRect worldBounds)
        {
            _worldBounds = worldBounds;
            _quadtree = new Quadtree(0, worldBounds);
            InitializeGeometryTemplates();
        }
        
        public void InitializeGPUContext(GRContext grContext)
        {
            _grContext = grContext;
        }
        
        public void UpdateSurface(SKImageInfo imageInfo)
        {
            if (_surfaceInfo != imageInfo)
            {
                _gpuSurface?.Dispose();
                
                if (_grContext != null)
                {
                    // 创建GPU加速表面
                    _gpuSurface = SKSurface.Create(_grContext, true, imageInfo);
                }
                
                _surfaceInfo = imageInfo;
                InvalidateCache();
            }
        }
        
        private void InitializeGeometryTemplates()
        {
            // 创建圆形模板 - 使用不同精度级别
            var circleTemplate = new SKPath();
            circleTemplate.AddCircle(0, 0, 1); // 单位圆
            _geometryTemplates[typeof(EntityCircle)] = new GeometryTemplate
            {
                BasePath = circleTemplate,
                BaseTransform = SKMatrix.Identity,
                BaseSize = 1.0f,
                Type = GeometryType.Circle
            };
            
            // 创建矩形模板
            var rectTemplate = new SKPath();
            rectTemplate.AddRect(new SKRect(-0.5f, -0.5f, 0.5f, 0.5f)); // 单位矩形
            _geometryTemplates[typeof(EntityRectangle)] = new GeometryTemplate
            {
                BasePath = rectTemplate,
                BaseTransform = SKMatrix.Identity,
                BaseSize = 1.0f,
                Type = GeometryType.Rectangle
            };
            
            // 创建线段模板
            var lineTemplate = new SKPath();
            lineTemplate.MoveTo(0, 0);
            lineTemplate.LineTo(1, 0); // 单位线段
            _geometryTemplates[typeof(EntityLine)] = new GeometryTemplate
            {
                BasePath = lineTemplate,
                BaseTransform = SKMatrix.Identity,
                BaseSize = 1.0f,
                Type = GeometryType.Line
            };
        }
        
        public void AddEntity(EntityBase entity)
        {
            if (entity != null)
            {
                _quadtree.Insert(entity);
                InvalidateCache();
            }
        }
        
        public void RemoveEntity(EntityBase entity)
        {
            if (entity != null)
            {
                // Quadtree重建 - 实际项目中应该使用支持删除的数据结构
                var allObjects = _quadtree.GetAllObjects().Where(obj => obj != entity).ToList();
                _quadtree.Clear();
                foreach (var obj in allObjects)
                {
                    _quadtree.Insert(obj);
                }
                _selectedEntities.Remove(entity);
                InvalidateCache();
            }
        }
        
        public void AddEntitiesBatch(IEnumerable<EntityBase> entities)
        {
            if (entities != null)
            {
                foreach (var entity in entities)
                {
                    _quadtree.Insert(entity);
                }
                InvalidateCache();
            }
        }
        
        public void UpdateEntity(EntityBase entity)
        {
            if (entity != null)
            {
                // 重新插入实体以更新其在Quadtree中的位置
                RemoveEntity(entity);
                AddEntity(entity);
            }
        }
        
        public IEnumerable<EntityBase> Query(SKRect queryRect)
        {
            return _quadtree.Query(queryRect)
                .Cast<EntityBase>()
                .Where(e => e.IsRenderable && e.IsVisible);
        }
        
        public void SetSelection(IEnumerable<EntityBase> selectedEntities)
        {
            _selectedEntities.Clear();
            if (selectedEntities != null)
            {
                foreach (var entity in selectedEntities)
                {
                    if (entity != null)
                    {
                        _selectedEntities.Add(entity);
                        entity.IsSelected = true;
                    }
                }
            }
            
            // 选择状态改变时需要刷新缓存
            InvalidateCache();
        }
        
        public void ClearSelection()
        {
            foreach (var entity in _selectedEntities)
            {
                entity.IsSelected = false;
            }
            _selectedEntities.Clear();
            
            // 选择状态改变时需要刷新缓存
            InvalidateCache();
        }
        
        public void Clear()
        {
            _quadtree.Clear();
            _selectedEntities.Clear();
            InvalidateCache();
        }
        
        private void InvalidateCache()
        {
            lock (_cacheLock)
            {
                foreach (var picture in _batchPictureCache.Values)
                {
                    picture?.Dispose();
                }
                _batchPictureCache.Clear();
            }
        }
        
        public void Render(SKCanvas canvas, SKRect visibleWorldRect)
        {
            var stopwatch = System.Diagnostics.Stopwatch.StartNew();
            var gpuStopwatch = System.Diagnostics.Stopwatch.StartNew();
            
            // 获取可见实体
            var visibleEntities = _quadtree.Query(visibleWorldRect)
                .Cast<EntityBase>()
                .Where(e => e.IsRenderable && e.IsVisible)
                .Take(Config.MaxRenderEntities)
                .ToList();
                
            // 按类型分组进行批量渲染
            var entityGroups = visibleEntities.GroupBy(e => e.GetType()).ToList();
            
            int batchedDrawCalls = 0;
            int cacheHits = 0;
            int cacheMisses = 0;
            bool usingGPU = _gpuSurface != null;
            
            var targetCanvas = usingGPU ? _gpuSurface.Canvas : canvas;
            
            if (usingGPU)
            {
                targetCanvas.Clear(SKColors.Transparent);
            }
            
            // 渲染每个实体组
            foreach (var group in entityGroups)
            {
                var entities = group.ToList();
                var entityType = group.Key;
                
                if (_geometryTemplates.TryGetValue(entityType, out var template))
                {
                    // 使用实例化渲染
                    RenderInstancedBatch(targetCanvas, entities, template, out int drawCalls, out int hits, out int misses);
                    batchedDrawCalls += drawCalls;
                    cacheHits += hits;
                    cacheMisses += misses;
                }
                else
                {
                    // 回退到标准渲染
                    RenderStandardBatch(targetCanvas, entities, out int drawCalls);
                    batchedDrawCalls += drawCalls;
                    cacheMisses += entities.Count;
                }
            }
            
            gpuStopwatch.Stop();
            
            // 如果使用GPU，将结果复制到主画布
            if (usingGPU && _gpuSurface != null)
            {
                using var image = _gpuSurface.Snapshot();
                canvas.DrawImage(image, 0, 0);
            }
            
            // 渲染选中实体
            RenderSelectionHighlights(canvas, visibleEntities.Where(e => e.IsSelected));
            
            stopwatch.Stop();
            
            // 更新统计
            _lastStats = new RenderStats
            {
                TotalEntities = visibleEntities.Count,
                RenderedEntities = visibleEntities.Count,
                BatchedDrawCalls = batchedDrawCalls,
                CacheHits = cacheHits,
                CacheMisses = cacheMisses,
                RenderTimeMs = stopwatch.ElapsedMilliseconds,
                GPUTimeMs = gpuStopwatch.ElapsedMilliseconds,
                UsingGPU = usingGPU,
                OptimizationInfo = $"Batches: {entityGroups.Count}, GPU: {(usingGPU ? "Yes" : "No")}"
            };
        }
        
        private void RenderInstancedBatch(SKCanvas canvas, List<EntityBase> entities, GeometryTemplate template, 
            out int drawCalls, out int cacheHits, out int cacheMisses)
        {
            drawCalls = 0;
            cacheHits = 0;
            cacheMisses = 0;
            
            // 创建批次缓存键
            var cacheKey = $"instanced_{template.Type}_{entities.Count}_{entities.GetHashCode()}";
            
            SKPicture batchPicture = null;
            lock (_cacheLock)
            {
                if (_batchPictureCache.TryGetValue(cacheKey, out batchPicture))
                {
                    cacheHits++;
                    canvas.DrawPicture(batchPicture);
                    drawCalls = 1;
                    return;
                }
            }
            
            // 创建新的批次Picture
            cacheMisses++;
            using var recorder = new SKPictureRecorder();
            var recordingCanvas = recorder.BeginRecording(_worldBounds);
            
            // 批量绘制相同几何形状的实例
            using var paint = GetPooledPaint();
            paint.Style = SKPaintStyle.Stroke;
            paint.IsAntialias = Config.EnableAntiAliasing;
            paint.StrokeWidth = 1.0f;
            
            // 合并相似的变换进行批量处理
            var transformGroups = entities
                .GroupBy(e => GetTransformSignature(e))
                .ToList();
                
            foreach (var transformGroup in transformGroups)
            {
                var groupEntities = transformGroup.ToList();
                
                // 为这个变换组创建合并路径
                using var mergedPath = GetPooledPath();
                
                foreach (var entity in groupEntities)
                {
                    var transform = CreateInstanceTransform(entity, template);
                    var instancePath = new SKPath(template.BasePath);
                    instancePath.Transform(in transform);
                    mergedPath.AddPath(instancePath);
                    instancePath.Dispose();
                }
                
                // 一次性绘制整个合并路径
                paint.Color = GetEntityColor(groupEntities.First());
                recordingCanvas.DrawPath(mergedPath, paint);
                drawCalls++;
                
                ReturnPooledPath(mergedPath);
            }
            
            ReturnPooledPaint(paint);
            
            batchPicture = recorder.EndRecording();
            
            // 缓存Picture
            lock (_cacheLock)
            {
                if (_batchPictureCache.Count > 50) // 限制缓存大小
                {
                    var oldestKey = _batchPictureCache.Keys.First();
                    _batchPictureCache[oldestKey]?.Dispose();
                    _batchPictureCache.Remove(oldestKey);
                }
                _batchPictureCache[cacheKey] = batchPicture;
            }
            
            canvas.DrawPicture(batchPicture);
        }
        
        private void RenderStandardBatch(SKCanvas canvas, List<EntityBase> entities, out int drawCalls)
        {
            drawCalls = entities.Count;
            
            using var paint = GetPooledPaint();
            paint.Style = SKPaintStyle.Stroke;
            paint.IsAntialias = Config.EnableAntiAliasing;
            paint.StrokeWidth = 1.0f;
            
            foreach (var entity in entities)
            {
                paint.Color = GetEntityColor(entity);
                DrawEntityDirect(canvas, entity, paint);
            }
            
            ReturnPooledPaint(paint);
        }
        
        private void DrawEntityDirect(SKCanvas canvas, EntityBase entity, SKPaint paint)
        {
            switch (entity)
            {
                case EntityCircle circle:
                    canvas.DrawOval(circle.BoundsSK, paint);
                    break;
                    
                case EntityLine line:
                    var bounds = line.BoundsSK;
                    canvas.DrawLine(bounds.Left, bounds.Top, bounds.Right, bounds.Bottom, paint);
                    break;
                    
                case EntityRectangle rect:
                    canvas.DrawRect(rect.BoundsSK, paint);
                    break;
                    
                default:
                    canvas.DrawRect(entity.BoundsSK, paint);
                    break;
            }
        }
        
        private SKMatrix CreateInstanceTransform(EntityBase entity, GeometryTemplate template)
        {
            var bounds = entity.BoundsSK;
            var scaleX = bounds.Width / template.BaseSize;
            var scaleY = bounds.Height / template.BaseSize;
            
            return SKMatrix.CreateScale(scaleX, scaleY)
                .PostConcat(SKMatrix.CreateTranslation(bounds.MidX, bounds.MidY));
        }
        
        private string GetTransformSignature(EntityBase entity)
        {
            var bounds = entity.BoundsSK;
            // 量化变换以创建批次
            var scaleX = Math.Round(bounds.Width / 10.0) * 10.0;
            var scaleY = Math.Round(bounds.Height / 10.0) * 10.0;
            return $"{scaleX}x{scaleY}";
        }
        
        private SKColor GetEntityColor(EntityBase entity)
        {
            if (entity.IsSelected)
                return SKColors.Red;
                
            // 根据实体类型返回不同颜色
            return entity switch
            {
                EntityCircle => SKColors.Blue,
                EntityLine => SKColors.Green,
                EntityRectangle => SKColors.Orange,
                _ => SKColors.Black
            };
        }
        
        private void RenderSelectionHighlights(SKCanvas canvas, IEnumerable<EntityBase> selectedEntities)
        {
            using var highlightPaint = new SKPaint
            {
                Style = SKPaintStyle.Stroke,
                Color = SKColors.Red,
                StrokeWidth = 2.0f,
                IsAntialias = true
            };
            
            foreach (var entity in selectedEntities)
            {
                var bounds = entity.BoundsSK;
                bounds.Inflate(2, 2); // 稍微放大高亮边框
                canvas.DrawRect(bounds, highlightPaint);
            }
        }
        
        // 对象池管理
        private SKPaint GetPooledPaint()
        {
            if (_paintPool.TryTake(out var paint))
            {
                paint.Reset();
                return paint;
            }
            return new SKPaint();
        }
        
        private void ReturnPooledPaint(SKPaint paint)
        {
            if (_paintPool.Count < 50) // 限制池大小
            {
                _paintPool.Add(paint);
            }
            else
            {
                paint.Dispose();
            }
        }
        
        private SKPath GetPooledPath()
        {
            if (_pathPool.TryTake(out var path))
            {
                path.Reset();
                return path;
            }
            return new SKPath();
        }
        
        private void ReturnPooledPath(SKPath path)
        {
            if (_pathPool.Count < 50) // 限制池大小
            {
                _pathPool.Add(path);
            }
            else
            {
                path.Dispose();
            }
        }
        
        public void Dispose()
        {
            _gpuSurface?.Dispose();
            
            // 清理Picture缓存
            lock (_cacheLock)
            {
                foreach (var picture in _batchPictureCache.Values)
                {
                    picture?.Dispose();
                }
                _batchPictureCache.Clear();
            }
            
            // 清理几何模板
            foreach (var template in _geometryTemplates.Values)
            {
                template.BasePath?.Dispose();
            }
            _geometryTemplates.Clear();
            
            // 清理对象池
            while (_paintPool.TryTake(out var paint))
            {
                paint.Dispose();
            }
            
            while (_pathPool.TryTake(out var path))
            {
                path.Dispose();
            }
        }
    }
} 