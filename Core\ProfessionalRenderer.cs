using SkiaSharp;
using System;
using System.Collections.Generic;
using System.Linq;
using McLaser.EditViewerSk.Entitys;
using McLaser.EditViewerSk.Spatial;
using McLaser.EditViewerSk.Base; // 添加Base命名空间引用
using System.Collections.Concurrent;
using System.Threading.Tasks;

namespace McLaser.EditViewerSk.Core
{
    /// <summary>
    /// 专业级渲染器：模仿Rhino的渲染品质，专门优化大量圆形
    /// 基于SkiaSharp Picture缓存 + 智能LOD + 优雅降级策略
    /// </summary>
    public class ProfessionalRenderer : IRenderer, IDisposable
    {
        private readonly Quadtree _quadtree;
        private readonly HashSet<EntityBase> _selectedEntities = new HashSet<EntityBase>();
        
        // Picture缓存系统：类似OpenGL显示列表，一次记录多次重绘
        private readonly Dictionary<string, CachedPicture> _pictureCache = new Dictionary<string, CachedPicture>();
        private readonly Dictionary<string, SKPath> _pathTemplates = new Dictionary<string, SKPath>();
        private readonly object _cacheLock = new object();
        
        // 渲染统计
        public struct RenderStats
        {
            public int TotalEntities;
            public int RenderedEntities;
            public float PixelsPerUnit;
            public string RenderMode;
            public int CachedPictures;
            public long RenderTimeMs;
            public bool UsingCache;
        }
        
        private RenderStats _lastStats;
        public RenderStats LastRenderStats => _lastStats;
        
        private struct CachedPicture
        {
            public SKPicture Picture;
            public DateTime CreateTime;
            public SKRect Bounds;
            public float PixelsPerUnit;
            public string Mode;
        }

        public ProfessionalRenderer(SKRect worldBounds)
        {
            _quadtree = new Quadtree(0, worldBounds);
            InitializePathTemplates();
        }

        private void InitializePathTemplates()
        {
            // 圆形模板：更丰富的细分级别
            CreateCircleTemplate("circle_smooth_6", 6);   // 六边形
            CreateCircleTemplate("circle_smooth_12", 12); // 12边形  
            CreateCircleTemplate("circle_smooth_20", 20); // 20边形
            CreateCircleTemplate("circle_smooth_32", 32); // 32边形
            CreateCircleTemplate("circle_smooth_64", 64); // 64边形
            CreateCircleTemplate("circle_perfect", 96);   // 接近完美圆形
            
            // 点样式模板
            CreatePointTemplate("point_pixel", 0.5f);
            CreatePointTemplate("point_small", 1.5f);
            CreatePointTemplate("point_medium", 2.5f);
            CreatePointTemplate("point_large", 4.0f);
        }

        private void CreateCircleTemplate(string key, int segments)
        {
            var path = new SKPath();
            var angleStep = 2 * Math.PI / segments;
            
            // 使用更精确的圆形生成
            path.MoveTo(1.0f, 0.0f);
            for (int i = 1; i <= segments; i++)
            {
                var angle = i * angleStep;
                path.LineTo((float)Math.Cos(angle), (float)Math.Sin(angle));
            }
            path.Close();
            
            _pathTemplates[key] = path;
        }

        private void CreatePointTemplate(string key, float size)
        {
            var path = new SKPath();
            // 使用圆形而非方形，更美观
            path.AddCircle(0, 0, size);
            _pathTemplates[key] = path;
        }

        public void AddEntity(EntityBase entity)
        {
            if (entity == null) return;
            _quadtree.Insert(entity);
            InvalidateCache();
        }

        public void AddEntitiesBatch(IEnumerable<EntityBase> entities)
        {
            var entityList = entities?.ToList();
            if (entityList == null || entityList.Count == 0) return;

            foreach (var entity in entityList)
            {
                _quadtree.Insert(entity);
            }
            InvalidateCache();
        }

        public void RemoveEntity(EntityBase entity)
        {
            if (entity == null) return;
            
            // Quadtree没有Remove方法，使用重建策略
            // 获取所有对象，移除目标对象，然后重建
            var allObjects = _quadtree.GetAllObjects();
            var filteredObjects = allObjects.Where(obj => obj != entity).ToList();
            
            _quadtree.Clear();
            foreach (var obj in filteredObjects)
            {
                _quadtree.Insert(obj);
            }
            
            _selectedEntities.Remove(entity);
            InvalidateCache();
        }

        public void UpdateEntity(EntityBase entity)
        {
            if (entity == null) return;
            InvalidateCache();
        }

        public void Clear()
        {
            _quadtree.Clear();
            _selectedEntities.Clear();
            InvalidateCache();
        }

        private void InvalidateCache()
        {
            lock (_cacheLock)
            {
                foreach (var cached in _pictureCache.Values)
                {
                    cached.Picture?.Dispose();
                }
                _pictureCache.Clear();
            }
        }

        public void Render(SKCanvas canvas, SKRect visibleWorldRect)
        {
            var stopwatch = System.Diagnostics.Stopwatch.StartNew();
            
            // 计算像素密度
            var pixelsPerUnit = CalculatePixelsPerUnit(canvas, visibleWorldRect);
            var renderMode = DetermineRenderMode(pixelsPerUnit);
            
            // 获取可见实体
            var visibleEntities = _quadtree.Query(visibleWorldRect)
                .Cast<EntityBase>()
                .Where(e => e.IsRenderable && e.IsVisible)
                .ToList();

            int renderedCount = 0;
            bool usingCache = false;

            // 根据渲染模式执行渲染
            switch (renderMode)
            {
                case "SimpleGeometry":
                case "StandardRender":
                case "HighQuality":
                    (renderedCount, usingCache) = RenderWithPictureCache(canvas, visibleEntities, visibleWorldRect, pixelsPerUnit, renderMode);
                    break;
            }

            // 渲染选中实体（始终高质量）
            RenderSelection(canvas, pixelsPerUnit);

            stopwatch.Stop();

            // 更新统计
            _lastStats = new RenderStats
            {
                TotalEntities = visibleEntities.Count,
                RenderedEntities = renderedCount,
                PixelsPerUnit = pixelsPerUnit,
                RenderMode = renderMode,
                CachedPictures = _pictureCache.Count,
                RenderTimeMs = stopwatch.ElapsedMilliseconds,
                UsingCache = usingCache
            };
        }

        private string DetermineRenderMode(float pixelsPerUnit)
        {
            // 强制使用最高质量渲染
            return "HighQuality";
        }

        private (int renderedCount, bool usingCache) RenderWithPictureCache(SKCanvas canvas, List<EntityBase> entities, 
            SKRect visibleWorldRect, float pixelsPerUnit, string renderMode)
        {
            // 使用Picture缓存进行高性能渲染
            var cacheKey = $"{renderMode}_{pixelsPerUnit:F3}_{visibleWorldRect.GetHashCode()}_{entities.Count}";
            
            SKPicture picture = null;
            bool usingCache = false;
            
            lock (_cacheLock)
            {
                if (_pictureCache.TryGetValue(cacheKey, out var cached))
                {
                    // 检查缓存是否仍然有效
                    var age = DateTime.Now - cached.CreateTime;
                    if (age.TotalSeconds < 30 && Math.Abs(cached.PixelsPerUnit - pixelsPerUnit) < 0.1f)
                    {
                        picture = cached.Picture;
                        usingCache = true;
                    }
                    else
                    {
                        // 缓存过期，移除
                        cached.Picture?.Dispose();
                        _pictureCache.Remove(cacheKey);
                    }
                }
            }
            
            if (picture == null)
            {
                picture = CreatePicture(entities, pixelsPerUnit, renderMode);
                
                lock (_cacheLock)
                {
                    // 限制缓存大小
                    if (_pictureCache.Count >= 8)
                    {
                        var oldestKey = _pictureCache.OrderBy(kvp => kvp.Value.CreateTime).First().Key;
                        _pictureCache[oldestKey].Picture?.Dispose();
                        _pictureCache.Remove(oldestKey);
                    }
                    
                    _pictureCache[cacheKey] = new CachedPicture
                    {
                        Picture = picture,
                        CreateTime = DateTime.Now,
                        Bounds = visibleWorldRect,
                        PixelsPerUnit = pixelsPerUnit,
                        Mode = renderMode
                    };
                }
            }
            
            canvas.DrawPicture(picture);
            return (entities.Count, usingCache);
        }

        private SKPicture CreatePicture(List<EntityBase> entities, float pixelsPerUnit, string renderMode)
        {
            var recorder = new SKPictureRecorder();
            var pictureCanvas = recorder.BeginRecording(SKRect.Create(200000, 200000));
            
            var paint = CreatePaintForMode(renderMode, pixelsPerUnit);
            
            foreach (var entity in entities)
            {
                if (!entity.IsRenderable || !entity.IsVisible || entity.IsSelected) continue;
                
                var path = CreateOptimizedPath(entity, pixelsPerUnit, renderMode);
                if (path != null)
                {
                    pictureCanvas.DrawPath(path, paint);
                    path.Dispose();
                }
            }
            
            paint.Dispose();
            var picture = recorder.EndRecording();
            recorder.Dispose();
            
            return picture;
        }

        private SKPath CreateOptimizedPath(EntityBase entity, float pixelsPerUnit, string renderMode)
        {
            if (entity is EntityCircle circle)
            {
                var radius = (float)circle.Radius;
                var screenSize = radius * pixelsPerUnit;
                
                // 根据屏幕大小和渲染模式选择合适的圆形模板
                string templateKey = renderMode switch
                {
                    "SimpleGeometry" => screenSize < 3 ? "circle_smooth_6" : "circle_smooth_12",
                    "StandardRender" => screenSize < 5 ? "circle_smooth_12" : 
                                       screenSize < 15 ? "circle_smooth_20" : "circle_smooth_32",
                    "HighQuality" => screenSize < 10 ? "circle_smooth_20" :
                                    screenSize < 30 ? "circle_smooth_32" :
                                    screenSize < 100 ? "circle_smooth_64" : "circle_perfect",
                    _ => "circle_smooth_12"
                };
                
                if (_pathTemplates.TryGetValue(templateKey, out var template))
                {
                    var path = new SKPath();
                    var matrix = SKMatrix.CreateScaleTranslation(
                        radius, radius,
                        (float)circle.Center.X, (float)circle.Center.Y);
                    template.Transform(in matrix, path);
                    return path;
                }
            }
            else if (entity is EntityPoint point)
            {
                var screenSize = 3.0f * pixelsPerUnit;
                
                string templateKey = screenSize < 2 ? "point_pixel" :
                                   screenSize < 5 ? "point_small" :
                                   screenSize < 10 ? "point_medium" : "point_large";
                
                if (_pathTemplates.TryGetValue(templateKey, out var template))
                {
                    var path = new SKPath();
                    var matrix = SKMatrix.CreateTranslation((float)point.EndPoint.X, (float)point.EndPoint.Y);
                    template.Transform(in matrix, path);
                    return path;
                }
            }
            
            return null;
        }

        private SKPaint CreatePaintForMode(string renderMode, float pixelsPerUnit)
        {
            return new SKPaint
            {
                Style = SKPaintStyle.Stroke,
                Color = SKColors.Black,
                StrokeWidth = renderMode switch
                {
                    "SimpleGeometry" => 0.5f,
                    "StandardRender" => Math.Max(0.5f, 1.0f / pixelsPerUnit),
                    "HighQuality" => Math.Max(0.3f, 1.2f / pixelsPerUnit),
                    _ => 1.0f
                },
                IsAntialias = renderMode != "SimpleGeometry"
            };
        }

        private void RenderSelection(SKCanvas canvas, float pixelsPerUnit)
        {
            if (_selectedEntities.Count == 0) return;
            
            using var paint = new SKPaint
            {
                Style = SKPaintStyle.Stroke,
                Color = SKColors.Red,
                StrokeWidth = Math.Max(1.5f, 3.0f / pixelsPerUnit),
                IsAntialias = true
            };
            
            foreach (var entity in _selectedEntities)
            {
                var path = CreateOptimizedPath(entity, pixelsPerUnit, "HighQuality");
                if (path != null)
                {
                    canvas.DrawPath(path, paint);
                    path.Dispose();
                }
            }
        }

        private float CalculatePixelsPerUnit(SKCanvas canvas, SKRect worldRect)
        {
            var transform = canvas.TotalMatrix;
            return Math.Min(Math.Abs(transform.ScaleX), Math.Abs(transform.ScaleY));
        }

        public IEnumerable<EntityBase> Query(SKRect queryRect)
        {
            return _quadtree.Query(queryRect).Cast<EntityBase>();
        }

        public void SetSelection(IEnumerable<EntityBase> selectedEntities)
        {
            _selectedEntities.Clear();
            if (selectedEntities != null)
            {
                foreach (var entity in selectedEntities)
                {
                    _selectedEntities.Add(entity);
                }
            }
        }

        public void ClearSelection()
        {
            _selectedEntities.Clear();
        }

        public void Dispose()
        {
            InvalidateCache();
            
            foreach (var path in _pathTemplates.Values)
            {
                path?.Dispose();
            }
            _pathTemplates.Clear();
        }
    }
} 