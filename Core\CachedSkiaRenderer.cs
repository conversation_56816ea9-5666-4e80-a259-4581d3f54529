
using SkiaSharp;
using System.Collections.Generic;
using McLaser.EditViewerSk.Entitys;
using System.Linq;
using McLaser.EditViewerSk.Spatial;
using System.Threading.Tasks;
using System.Collections.Concurrent;
using System;

namespace McLaser.EditViewerSk.Core
{
    public class CachedSkiaRenderer : IRenderer
    {
        private readonly Quadtree _quadtree;
        private readonly Dictionary<EntityBase, SKPath> _pathCache = new Dictionary<EntityBase, SKPath>();
        
        // 优化：预创建Paint对象避免重复分配
        private readonly SKPaint _defaultPaint = new SKPaint { Style = SKPaintStyle.Stroke, IsAntialias = true, StrokeWidth = 1, Color = SKColors.Black };
        private readonly SKPaint _selectedPaint = new SKPaint { Style = SKPaintStyle.Stroke, IsAntialias = true, StrokeWidth = 2, Color = SKColors.Red };
        
        // Circle特化优化：缓存相同半径的圆形路径
        private readonly Dictionary<float, SKPath> _circlePathCache = new Dictionary<float, SKPath>();
        private readonly object _cacheLock = new object();

        // 性能统计
        public struct RenderStats
        {
            public int TotalEntities;
            public int QueriedEntities;
            public int RenderedEntities;
            public int SkippedByLOD;
            public float CurrentScale;
            public long RenderTimeMs;
            public string LODInfo; // 添加LOD信息
        }

        private RenderStats _lastRenderStats;
        public RenderStats LastRenderStats => _lastRenderStats;

        // 添加调试方法
        public void EnableLODDebugging(bool enable)
        {
            _lodDebuggingEnabled = enable;
        }

        private bool _lodDebuggingEnabled = false;

        public CachedSkiaRenderer(SKRect worldBounds)
        {
            _quadtree = new Quadtree(0, worldBounds);
        }

        public void AddEntity(EntityBase entity)
        {
            if (entity == null || _pathCache.ContainsKey(entity)) return;

            var path = CreatePathForEntity(entity);
            if (path != null)
            {
                lock (_cacheLock)
                {
                    _pathCache[entity] = path;
                    _quadtree.Insert(entity);
                }
            }
        }

        // 新增：批量添加实体的优化方法
        public void AddEntitiesBatch(IEnumerable<EntityBase> entities)
        {
            var entityList = entities.Where(e => e != null && !_pathCache.ContainsKey(e)).ToList();
            if (entityList.Count == 0) return;

            // 并行创建路径以提升性能
            var pathPairs = new ConcurrentBag<(EntityBase entity, SKPath path)>();
            
            Parallel.ForEach(entityList, entity =>
            {
                var path = CreatePathForEntity(entity);
                if (path != null)
                {
                    pathPairs.Add((entity, path));
                }
            });

            // 批量添加到缓存和四叉树
            lock (_cacheLock)
            {
                foreach (var (entity, path) in pathPairs)
                {
                    _pathCache[entity] = path;
                    _quadtree.Insert(entity);
                }
            }
        }

        public void RemoveEntity(EntityBase entity)
        {
            if (entity != null && _pathCache.TryGetValue(entity, out var path))
            {
                lock (_cacheLock)
                {
                    _pathCache.Remove(entity);
                    path.Dispose();
                    RebuildQuadtree();
                }
            }
        }

        public void UpdateEntity(EntityBase entity)
        {
            if (entity == null) return;

            lock (_cacheLock)
            {
                if (_pathCache.TryGetValue(entity, out var oldPath))
                {
                    oldPath.Dispose();
                }

                var newPath = CreatePathForEntity(entity);
                if (newPath != null)
                {
                    _pathCache[entity] = newPath;
                    RebuildQuadtree();
                }
            }
        }

        public void Clear()
        {
            lock (_cacheLock)
            {
                foreach (var path in _pathCache.Values) path.Dispose();
                _pathCache.Clear();
                
                foreach (var path in _circlePathCache.Values) path.Dispose();
                _circlePathCache.Clear();
                
                _quadtree.Clear();
            }
        }

        private void RebuildQuadtree()
        {
            _quadtree.Clear();
            foreach (var entity in _pathCache.Keys)
            {
                _quadtree.Insert(entity);
            }
        }

        public void Render(SKCanvas canvas, SKRect visibleWorldRect)
        {
            var stopwatch = System.Diagnostics.Stopwatch.StartNew();
            
            List<IQuadtreeObject> entitiesToRender;
            
            lock (_cacheLock)
            {
                entitiesToRender = _quadtree.Query(visibleWorldRect);
            }

            // 计算当前缩放级别用于LOD
            var currentScale = CalculateCurrentScale(canvas, visibleWorldRect);
            
            // LOD渲染：根据缩放级别过滤和简化图元
            var visibleEntities = ApplyLODFiltering(entitiesToRender.Cast<EntityBase>(), currentScale, visibleWorldRect).ToList();

            // 按类型分组以优化渲染
            var selectedEntities = new List<(EntityBase entity, SKPath path)>();
            var normalEntities = new List<(EntityBase entity, SKPath path)>();

            foreach (var entity in visibleEntities)
            {
                if (!_pathCache.TryGetValue(entity, out var path)) continue;
                
                if (entity.IsSelected)
                    selectedEntities.Add((entity, path));
                else
                    normalEntities.Add((entity, path));
            }

            // 根据缩放级别调整Paint设置
            AdjustPaintForScale(currentScale);

            // 批量渲染普通实体
            foreach (var (entity, path) in normalEntities)
            {
                canvas.DrawPath(path, _defaultPaint);
            }

            // 批量渲染选中实体
            foreach (var (entity, path) in selectedEntities)
            {
                canvas.DrawPath(path, _selectedPaint);
            }

            stopwatch.Stop();

            // 更新性能统计
            var lodInfo = $"Scale: {currentScale:F6}, MinSize: 0.5px, VerySmall: 0.1px";
            if (_lodDebuggingEnabled && _lastRenderStats.SkippedByLOD > 0)
            {
                System.Diagnostics.Debug.WriteLine($"LOD: {lodInfo}, Skipped: {entitiesToRender.Count - visibleEntities.Count}");
            }

            _lastRenderStats = new RenderStats
            {
                TotalEntities = _pathCache.Count,
                QueriedEntities = entitiesToRender.Count,
                RenderedEntities = normalEntities.Count + selectedEntities.Count,
                SkippedByLOD = entitiesToRender.Count - visibleEntities.Count,
                CurrentScale = currentScale,
                RenderTimeMs = stopwatch.ElapsedMilliseconds,
                LODInfo = lodInfo
            };
        }

        // LOD相关方法
        private float CalculateCurrentScale(SKCanvas canvas, SKRect visibleWorldRect)
        {
            if (visibleWorldRect.IsEmpty) return 1.0f;
            
            var canvasSize = canvas.DeviceClipBounds;
            if (canvasSize.Width <= 0 || canvasSize.Height <= 0) return 1.0f;
            
            var scaleX = canvasSize.Width / visibleWorldRect.Width;
            var scaleY = canvasSize.Height / visibleWorldRect.Height;
            
            var scale = Math.Min(scaleX, scaleY);
            
            // 限制缩放范围，避免极端值
            scale = Math.Max(0.001f, Math.Min(scale, 1000.0f));
            
            return scale;
        }

        private IEnumerable<EntityBase> ApplyLODFiltering(IEnumerable<EntityBase> entities, float scale, SKRect visibleRect)
        {
            // 调整LOD参数，使其更保守
            const float MinVisibleSize = 0.5f; // 减小最小可见像素大小
            const float VerySmallThreshold = 0.1f; // 非常小的图元阈值

            foreach (var entity in entities)
            {
                if (!entity.IsRenderable || !entity.IsVisible) continue;
                
                // 基本可见性检查
                var entityBounds = entity.BoundsSK;
                if (entityBounds.IsEmpty || !visibleRect.IntersectsWith(entityBounds)) continue;
                
                // 当缩放级别很小时（视图很大），减少LOD过滤
                if (scale < 0.01f)
                {
                    // 极小缩放时，只过滤掉非常小的图元
                    var worldSize = Math.Max(entityBounds.Width, entityBounds.Height);
                    if (worldSize < 0.01f) // 世界坐标中小于0.01的图元
                    {
                        continue;
                    }
                    yield return entity;
                    continue;
                }
                
                // 正常LOD过滤：根据屏幕像素大小决定是否渲染
                var screenSize = Math.Max(entityBounds.Width * scale, entityBounds.Height * scale);
                
                if (screenSize < VerySmallThreshold)
                {
                    // 只跳过非常小的图元
                    continue;
                }
                else if (screenSize < MinVisibleSize)
                {
                    // 小图元：优先渲染重要图元
                    if (entity.IsSelected || IsImportantEntity(entity))
                    {
                        yield return entity;
                    }
                    else if (scale > 0.1f) // 在较大缩放级别时仍然渲染
                    {
                        yield return entity;
                    }
                }
                else
                {
                    // 正常大小的图元始终渲染
                    yield return entity;
                }
            }
        }

        private bool IsImportantEntity(EntityBase entity)
        {
            // 扩展重要图元的定义，包括Circle
            return entity is EntityLine || 
                   entity is EntityCircle || 
                   entity is EntityArc ||
                   entity.IsSelected;
        }

        private void AdjustPaintForScale(float scale)
        {
            // 根据缩放级别调整线宽，保持视觉一致性
            var baseStrokeWidth = Math.Max(0.5f, 1.0f / Math.Max(scale, 0.1f));
            
            _defaultPaint.StrokeWidth = baseStrokeWidth;
            _selectedPaint.StrokeWidth = baseStrokeWidth * 2;
            
            // 在很小的缩放级别下关闭抗锯齿以提升性能
            bool useAntiAlias = scale > 0.1f;
            _defaultPaint.IsAntialias = useAntiAlias;
            _selectedPaint.IsAntialias = useAntiAlias;
        }

        public IEnumerable<EntityBase> Query(SKRect queryRect)
        {
            lock (_cacheLock)
            {
                return _quadtree.Query(queryRect).Cast<EntityBase>();
            }
        }

        public void SetSelection(IEnumerable<EntityBase> selectedEntities)
        {
            lock (_cacheLock)
            {
                // 清除之前的选择状态
                foreach (var entity in _pathCache.Keys)
                {
                    entity.IsSelected = false;
                }
                
                // 设置新的选择状态
                foreach (var entity in selectedEntities)
                {
                    if (_pathCache.ContainsKey(entity))
                    {
                        entity.IsSelected = true;
                    }
                }
            }
        }

        public void ClearSelection()
        {
            lock (_cacheLock)
            {
                foreach (var entity in _pathCache.Keys)
                {
                    entity.IsSelected = false;
                }
            }
        }

        private SKPath CreatePathForEntity(EntityBase entity)
        {
            var path = new SKPath();
            switch (entity)
            {
                case EntityLine line:
                    path.MoveTo((float)line.StartPoint.X, (float)line.StartPoint.Y);
                    path.LineTo((float)line.EndPoint.X, (float)line.EndPoint.Y);
                    break;
                    
                case EntityCircle circle:
                    // Circle特化优化：重用相同半径的路径
                    return CreateOptimizedCirclePath(circle);
                    
                case EntityArc arc:
                    var rect = SKRect.Create((float)(arc.Center.X - arc.Radius), (float)(arc.Center.Y - arc.Radius), (float)(arc.Radius * 2), (float)(arc.Radius * 2));
                    path.AddArc(rect, (float)arc.StartAngle, (float)arc.SweepAngle);
                    break;
                    
                case EntityPolyline2D polyline2d:
                    CreatePolyline2DPath(polyline2d, path);
                    break;
                    
                case EntityLwPolyline polyline:
                    CreateLwPolylinePath(polyline, path);
                    break;
                    
                default:
                    path.Dispose();
                    return null;
            }
            return path;
        }

        // Circle特化优化方法
        private SKPath CreateOptimizedCirclePath(EntityCircle circle)
        {
            var radius = (float)circle.Radius;
            var center = circle.Center;
            
            // 对于小半径的圆，重用路径模板
            if (radius <= 10.0f)
            {
                lock (_cacheLock)
                {
                    if (!_circlePathCache.TryGetValue(radius, out var templatePath))
                    {
                        templatePath = new SKPath();
                        templatePath.AddCircle(0, 0, radius);
                        _circlePathCache[radius] = templatePath;
                    }

                    // 创建变换后的路径
                    var transformedPath = new SKPath();
                    var matrix = SKMatrix.CreateTranslation((float)center.X, (float)center.Y);
                    templatePath.Transform(in matrix, transformedPath);
                    return transformedPath;
                }
            }
            else
            {
                // 大半径圆直接创建
                var path = new SKPath();
                path.AddCircle((float)center.X, (float)center.Y, radius);
                return path;
            }
        }

        private void CreatePolyline2DPath(EntityPolyline2D polyline2d, SKPath path)
        {
            if (polyline2d.Entities != null && polyline2d.Entities.Count > 0)
            {
                foreach (var subEntity in polyline2d.Entities)
                {
                    var subPath = CreatePathForEntity(subEntity);
                    if (subPath != null)
                    {
                        path.AddPath(subPath);
                        subPath.Dispose();
                    }
                }
            }
            else if (polyline2d.Points.Count > 1)
            {
                path.MoveTo((float)polyline2d.Points[0].X, (float)polyline2d.Points[0].Y);
                for (int i = 1; i < polyline2d.Points.Count; i++)
                {
                    path.LineTo((float)polyline2d.Points[i].X, (float)polyline2d.Points[i].Y);
                }
                if (polyline2d.IsClosed)
                {
                    path.Close();
                }
            }
        }

        private void CreateLwPolylinePath(EntityLwPolyline polyline, SKPath path)
        {
            if (polyline.Vertexes.Count > 1)
            {
                path.MoveTo((float)polyline.Vertexes[0].X, (float)polyline.Vertexes[0].Y);
                for (int i = 1; i < polyline.Vertexes.Count; i++)
                {
                    path.LineTo((float)polyline.Vertexes[i].X, (float)polyline.Vertexes[i].Y);
                }
                if (polyline.IsClosed)
                {
                    path.Close();
                }
            }
        }
    }
}
